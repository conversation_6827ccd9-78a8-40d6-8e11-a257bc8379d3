export interface User {
  id?: string;
  email: string;
  token?: string;
}

export interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string) => Promise<void>;
  logout: () => void;
}

export interface TaskResponse {
  _id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'completed';
  meetingId: string;
  assignedTo?: Array<{ _id: string; name: string }>;
  createdAt?: string;
  updatedAt?: string;
  deadline?: string | null;
}

export interface Task {
  _id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'completed';
  meetingId: string;
  wardId: string;
  meetingTitle: string;
  wardName: string;
  assignedTo?: Array<{ _id: string; name: string }>;
  createdAt?: string;
  updatedAt?: string;
  deadline?: string | null;
}

export interface SuggestedTask {
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'completed';
  deadline?: string | null;
}

export interface ImageUploadResponse {
  imageUrl: string;
  extractedText: string;
  suggestedTasks: SuggestedTask[];
}

export interface Directory {
  _id: string;
  userName: string;
  designation?: string;
  description?: string;
  email: string;
  phone?: string;
  address?: string;
  createdBy: string;
  createdAt: string;
  reportingManager: string[];
}

export interface Ward {
  _id: string;
  title: string;
  description?: string;
  members?: Array<{ _id: string; name: string }>;
  resolver?: [{ _id: string; userName: string }];
  meetingFrequency?: string;
  zone: string;
  district?: string;
}

export interface Meeting {
  _id: string;
  title: string;
  description?: string;
  startDate: string;
  endDate: string;
  status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled';
  ward: string | Ward; // Can be populated or just ID
  wardId?: string; // For backward compatibility
  agenda?: string;
  tasks?: Task[];
  taskCount?: number;
  attendees: Array<{ _id: string; name: string }>;
  createdAt: string;
  updatedAt: string;
}
