import React, { useEffect, useState } from 'react';
import { Modal } from '@mantine/core';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface MapModalProps {
  opened: boolean;
  onClose: () => void;
  coordinates: number[];
}

const MapModal: React.FC<MapModalProps> = ({ opened, onClose, coordinates }) => {
  const [shouldRenderMap, setShouldRenderMap] = useState(false);
  
  // Convert coordinates from [lng, lat] to [lat, lng] for Leaflet
  const leafletCoordinates: [number, number] = [coordinates[1], coordinates[0]];

  useEffect(() => {
    if (opened) {
      // Delay map rendering to ensure modal is fully opened
      const timer = setTimeout(() => {
        setShouldRenderMap(true);
      }, 100);
      return () => clearTimeout(timer);
    } else {
      setShouldRenderMap(false);
    }
  }, [opened]);

  const styles = {
    container: {
      width: '100%',
      height: '100%',
      borderBottomLeftRadius: '8px',
      borderBottomRightRadius: '8px',
      minHeight: '500px'
    },
    loadingContainer: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100%',
      backgroundColor: '#f3f4f6'
    },
    loadingContent: {
      textAlign: 'center' as const
    },
    spinner: {
      animation: 'spin 1s linear infinite',
      borderRadius: '50%',
      height: '32px',
      width: '32px',
      borderWidth: '2px',
      borderStyle: 'solid',
      borderColor: 'transparent transparent #3b82f6 transparent',
      margin: '0 auto 8px auto'
    },
    loadingText: {
      color: '#4b5563'
    },
    mapContainer: {
      height: '100%',
      width: '100%',
      borderBottomLeftRadius: '8px',
      borderBottomRightRadius: '8px'
    }
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      size="xl"
      title="Location Details"
      centered
      styles={{
        body: { padding: 0, height: '70vh' },
        header: { backgroundColor: '#ffffff', color: '#1f2937', borderBottom: '1px solid #e5e7eb' },
      }}
    >
      <div style={styles.container}>
        {shouldRenderMap ? (
          <MapContainer
            key={`map-${opened}`} // Force re-mount when modal opens
            center={leafletCoordinates}
            zoom={15}
            style={styles.mapContainer}
          >
            <TileLayer
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            />
            <Marker position={leafletCoordinates}>
              <Popup>
                Complaint Location<br />
                Coordinates: {coordinates[1].toFixed(6)}, {coordinates[0].toFixed(6)}
              </Popup>
            </Marker>
          </MapContainer>
        ) : (
          <div style={styles.loadingContainer}>
            <div style={styles.loadingContent}>
              <div style={styles.spinner}></div>
              <p style={styles.loadingText}>Loading map...</p>
            </div>
          </div>
        )}
      </div>
      
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
    </Modal>
  );
};

export default MapModal;