import { useState } from "react";
import { Routes, Route, useNavigate, useLocation } from "react-router-dom";
import {
  AppShell,
  Text,
  Burger,
  useMantineTheme,
  Group,
  Button,
  NavLink,
  Stack,
  Breadcrumbs,
  Anchor,

} from "@mantine/core";
import { IconUsers, IconAddressBook, IconDashboard } from "@tabler/icons-react";
import { useAuth } from "../contexts/AuthContext";
import Login from "../pages/Login";
import Register from "../pages/Register";
import Dashboard from "../pages/Dashboard";
import Meetings from "../pages/Meetings";
import MeetingDetails from "../pages/MeetingDetails";
import Directory from "../pages/Directory";
import PrivateRoute from "./PrivateRoute";
import Wards from "../pages/Wards";

export default function AppLayout() {
  const theme = useMantineTheme();
  const [opened, setOpened] = useState(false);
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  const navItems = [
    { label: "Dashboard", icon: IconDashboard, path: "/" },
    { label: "Wards", icon: IconUsers, path: "/wards" },
    { label: "Directory", icon: IconAddressBook, path: "/directory" },
  ];

  // Generate breadcrumbs based on current location
  const generateBreadcrumbs = () => {
    const path = location.pathname;
    const breadcrumbs = [];

    // Always start with Dashboard
    breadcrumbs.push(
      <Anchor key="dashboard" onClick={() => navigate('/')}>
        Dashboard
      </Anchor>
    );

    if (path === '/') {
      return breadcrumbs;
    }

    if (path === '/wards') {
      breadcrumbs.push(<Text key="wards">Wards</Text>);
    } else if (path === '/directory') {
      breadcrumbs.push(<Text key="directory">Directory</Text>);
    } else if (path.startsWith('/ward/')) {
      const pathParts = path.split('/');
      const wardId = pathParts[2];

      breadcrumbs.push(
        <Anchor key="wards" onClick={() => navigate('/wards')}>
          Wards
        </Anchor>
      );

      if (pathParts.length >= 4 && pathParts[3] === 'meetings') {
        breadcrumbs.push(<Text key="ward">Ward</Text>);

        if (pathParts.length === 4) {
          breadcrumbs.push(<Text key="meetings">Meetings</Text>);
        } else if (pathParts.length >= 5) {
          breadcrumbs.push(
            <Anchor key="meetings" onClick={() => navigate(`/ward/${wardId}/meetings`)}>
              Meetings
            </Anchor>
          );
          breadcrumbs.push(<Text key="meeting">Meeting</Text>);
        }
      }
    }

    return breadcrumbs;
  };

  // Check if current route is a public route (login/register)
  const isPublicRoute = location.pathname === '/login' || location.pathname === '/register';

  // If it's a public route, render without AppShell
  if (isPublicRoute) {
    return (
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
      </Routes>
    );
  }

  // For authenticated routes, render with AppShell
  return (
    <AppShell
      layout="alt"
      padding="md"
      navbar={{
        width: { base: 200, lg: 300 },
        breakpoint: "sm",
        collapsed: { mobile: !opened },
      }}
      header={{
        height: 70,
      }}
    >
      <AppShell.Navbar p="md">
        <Stack>
          <Text size="lg" fw={600} mb="md">
            DCMAMP
          </Text>

          {navItems.map((item) => (
            <NavLink
              key={item.path}
              label={item.label}
              leftSection={<item.icon size={16} />}
              onClick={() => navigate(item.path)}
              active={location.pathname === item.path}
            />
          ))}
        </Stack>
      </AppShell.Navbar>

      <AppShell.Header>
        <Group h="100%" px="md">
          <Burger
            hiddenFrom="sm"
            opened={opened}
            onClick={() => setOpened((o) => !o)}
            size="sm"
            color={theme.colors.gray[6]}
          />

          <Group justify="space-between" style={{ flex: 1 }}>
            <Breadcrumbs>
              {generateBreadcrumbs()}
            </Breadcrumbs>
            {user && (
              <Group>
                <Text>{user.email}</Text>
                <Button variant="light" onClick={handleLogout}>
                  Logout
                </Button>
              </Group>
            )}
          </Group>
        </Group>
      </AppShell.Header>

      <AppShell.Main>
        <Routes>
          <Route path="/" element={<PrivateRoute><Dashboard /></PrivateRoute>} />
          <Route path="/wards" element={<PrivateRoute><Wards /></PrivateRoute>} />
          <Route path="/ward/:wardId/meetings" element={<PrivateRoute><Meetings /></PrivateRoute>} />
          <Route path="/ward/:wardId/meetings/:meetingId" element={<PrivateRoute><MeetingDetails /></PrivateRoute>} />
          <Route path="/directory" element={<PrivateRoute><Directory /></PrivateRoute>} />
        </Routes>
      </AppShell.Main>
    </AppShell>
  );
}
