import { useState } from "react";
import { Routes, Route, useNavigate, useLocation } from "react-router-dom";
import {
  AppShell,
  Text,
  Burger,
  useMantineTheme,
  Group,
  Button,
  NavLink,
  Stack,
  Breadcrumbs,
  Anchor,
} from "@mantine/core";
import { IconUsers, IconAddressBook, IconDashboard, IconContract, IconBriefcase2 } from "@tabler/icons-react";
import { useAuth } from "../contexts/AuthContext";
import Login from "../pages/Login";
import Register from "../pages/Register";
import Dashboard from "../pages/Dashboard";
import Meetings from "../pages/Meetings";
import MeetingDetails from "../pages/MeetingDetails";
import Directory from "../pages/Directory";
import PrivateRoute from "./PrivateRoute";
import Wards from "../pages/Wards";
import Complaints from "@/pages/Complaints";
import Work from "@/pages/Work";
import ComplaintDetails from "@/pages/ComplaintDetails";
import WorkDetails from "@/pages/WorkDetails";
import NotFoundPage from "@/pages/NotFoundPage";

export default function AppLayout() {
  const theme = useMantineTheme();
  const [opened, setOpened] = useState(false);
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  const navItems = [
    { label: "Dashboard", icon: IconDashboard, path: "/" },
    { label: "Wards", icon: IconUsers, path: "/wards" },
    { label: "Directory", icon: IconAddressBook, path: "/directory" },
    { label: "Complaints", icon: IconContract, path:"/complaints"},
    { label: "Work", icon: IconBriefcase2, path:"/works"}
  ];

  // Generate breadcrumbs based on current location
  const generateBreadcrumbs = () => {
    const path = location.pathname;
    const breadcrumbs = [];

    // Always start with Dashboard
    breadcrumbs.push(
      <Anchor key="dashboard" onClick={() => navigate('/')}>
        Dashboard
      </Anchor>
    );

    if (path === '/') {
      return breadcrumbs;
    }

    if (path === '/wards') {
      breadcrumbs.push(<Text key="wards">Wards</Text>);
    } else if (path === '/directory') {
      breadcrumbs.push(<Text key="directory">Directory</Text>);
    } else if(path.startsWith('/complaints')) {
      const pathParts = path.split('/');

      if(pathParts.length >=3){
        breadcrumbs.push(
          [ <Anchor key="complaints" onClick={() => navigate('/complaints')}> Complaints</Anchor>,
            <Text key='complaint'>Complaint</Text>
          ])
      } else{
        breadcrumbs.push(<Text key='complaint'>Complaints</Text>)
      }
    } else if(path.startsWith('/work')) {
      const pathParts = path.split('/');

      if(pathParts.length >=3){
        breadcrumbs.push([
          <Anchor key="work" onClick={() => navigate('/works')}> Works</Anchor>,
          <Text key='complaint'>Work</Text>
        ])
      } else{
        breadcrumbs.push(<Text key='complaint'>Works</Text>)
      }
    }
    if(breadcrumbs.length === 1 ) return [];
    return breadcrumbs;
  };

  // Check if current route is a public route (login/register)
  const isPublicRoute = location.pathname === '/login' || location.pathname === '/register';

  // If it's a public route, render without AppShell
  if (isPublicRoute) {
    return (
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
      </Routes>
    );
  }

  return (
    <AppShell
      layout="alt"
      padding="md"
      navbar={{
        width: { base: 200, lg: 300 },
        breakpoint: "sm",
        collapsed: { mobile: !opened },
      }}
      header={{
        height: 70,
      }}
    >
      <AppShell.Navbar p="md">
        <Stack>
          <Text size="lg" fw={600} mb="md">
            GMC
          </Text>

          {navItems.map((item) => {
            return <NavLink
              key={item.path}
              label={item.label}
              leftSection={<item.icon size={16} />}
              onClick={() => navigate(item.path)}
              active={location.pathname === item.path || location.pathname.startsWith(item.path + '/')}
            />
          })}
        </Stack>
      </AppShell.Navbar>

      <AppShell.Header>
        <Group h="100%" px="md">
          <Burger
            hiddenFrom="sm"
            opened={opened}
            onClick={() => setOpened((o) => !o)}
            size="sm"
            color={theme.colors.gray[6]}
          />

          <Group justify="space-between" style={{ flex: 1 }}>
            <Breadcrumbs>
              {generateBreadcrumbs()}
            </Breadcrumbs>
            {user && (
              <Group>
                <Text>{user.email}</Text>
                <Button variant="light" onClick={handleLogout}>
                  Logout
                </Button>
              </Group>
            )}
          </Group>
        </Group>
      </AppShell.Header>

      <AppShell.Main>
        <Routes>
          <Route path="/" element={<PrivateRoute><Dashboard /></PrivateRoute>} />
          <Route path="/wards" element={<PrivateRoute><Wards /></PrivateRoute>} />
          <Route path="/ward/:wardId/meetings" element={<PrivateRoute><Meetings /></PrivateRoute>} />
          <Route path="/ward/:wardId/meetings/:meetingId" element={<PrivateRoute><MeetingDetails /></PrivateRoute>} />
          <Route path="/directory" element={<PrivateRoute><Directory /></PrivateRoute>} />
          <Route path="/complaints" element={<PrivateRoute><Complaints/></PrivateRoute>}/>
          <Route path="/complaints/:id" element={<PrivateRoute><ComplaintDetails/></PrivateRoute>} />
          <Route path="/works/:id" element={<PrivateRoute><WorkDetails/></PrivateRoute>} />
          <Route path="/works" element={<PrivateRoute><Work/></PrivateRoute>}/>
          <Route path="*" element={<NotFoundPage/>}/>
        </Routes>
      </AppShell.Main>
    </AppShell>
  );
}
