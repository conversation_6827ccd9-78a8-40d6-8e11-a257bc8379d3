import React, { useState } from 'react';
import {
	AspectRatio,
	Paper,
	Grid,
	Group,
	Text,
	Title,
	Badge,
	SimpleGrid,
	Image,
	Divider,
	Stack,
	Card,
	Alert,
	Button,
	Box,
	Skeleton,
	Transition,
	Tooltip,
	ActionIcon,
	Flex,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IconInfoCircle, IconMapPin, IconCalendar, IconUser, IconPhoto, IconEye, IconTimeline } from '@tabler/icons-react';

import { complaintStatus, ComplaintStatus } from '@/types';
import { GalleryViewer } from './ImageModal';
import { getComplaintStatusText } from '@/libs/constant';

interface ResolutionInfo {
	description: string;
	resolvedBy: string;
	images: Array<{ url: string; contentType: string }>;
	aiReview?: string;
}

interface ProblemDetailsProps {
	id: string;
	title: string;
	description: string;
	status: ComplaintStatus;
	ward: string;
	reportedBy: string;
	reportedOn: Date;
	location: { lat: number; lng: number };
	problemImages: string[];
	resolution?: ResolutionInfo;
	showCloseIssueButton: boolean;
	onCloseIssue?: () => Promise<void>;
}

/* helpers */
const statusColor: Record<string, string> = {
	[complaintStatus.unresolved]: 'red.6',
	[complaintStatus.sendForReview]: 'orange.6',
	[complaintStatus.aiReviewGenerated]: 'violet.6',
	[complaintStatus.completed]: 'teal.6',
};

export const ProblemDetails: React.FC<ProblemDetailsProps> = ({
	id,
	title,
	description,
	status,
	ward,
	reportedBy,
	reportedOn,
	location,
	problemImages,
	resolution,
	showCloseIssueButton,
	onCloseIssue,
}) => {
	const [viewerOpened, { open: openViewer, close: closeViewer }] = useDisclosure(false);
	const [viewerImgs, setViewerImgs] = useState<string[]>([]);
	const [viewerStart, setViewerStart] = useState(0);
	const [mapLoaded, setMapLoaded] = useState(false);
	const [isClosingIssue, setIsClosingIssue] = useState(false);
	const [imageLoadStates, setImageLoadStates] = useState<Record<string, boolean>>({});

	const launchViewer = (imgs: string[], startIdx: number) => {
		setViewerImgs(imgs);
		setViewerStart(startIdx);
		openViewer();
	};

	const handleCloseIssue = async () => {
		if (!onCloseIssue) return;
		setIsClosingIssue(true);
		try {
			await onCloseIssue();
		} finally {
			setIsClosingIssue(false);
		}
	};

	const handleImageLoad = (url: string) => {
		setImageLoadStates(prev => ({ ...prev, [url]: true }));
	};

	const reviewColor = resolution?.aiReview
		? /no\s+issue|looks\s+good|approved|excellent|perfect|successfully/i.test(resolution.aiReview)
			? 'green'
			: /partial|could\s+be\s+better|minor|needs\s+attention/i.test(resolution.aiReview)
				? 'yellow'
				: /poor|failed|incomplete|unsatisfactory/i.test(resolution.aiReview)
					? 'red'
					: 'blue'
		: 'blue';

	return (
		<>
			<Paper p="xs" radius="md" withBorder style={{ position: 'relative' }} shadow="sm">
				<Group justify="space-between" mb="md" align="flex-start">
					<Box style={{ flex: 1, marginRight: 16 }}>
						<Title order={2} fw={700} size="h3" mb={4} c="dark.8">
							{title}
						</Title>
						<Text size="sm" c="dimmed" style={{ lineHeight: 1.4 }}>
							Issue #{id}
						</Text>
					</Box>

					<Badge
						color={statusColor[status as unknown as string] ?? 'gray'}
						size="lg"
						tt="capitalize"
						variant="light"
						style={{ 
							paddingTop: 8, 
							paddingBottom: 8,
							paddingLeft: 12,
							paddingRight: 12,
							fontSize: 12,
							fontWeight: 600
						}}
					>
						{getComplaintStatusText(status)}
					</Badge>
				</Group>
				<Paper p="xs" withBorder mb={'md'}>
					<Flex mb="md" gap={64}>
						<SimpleGrid cols={2} w={"50%"}>
							<Group gap="sm">
								<IconMapPin size={16} color="var(--mantine-color-gray-6)" />
								<Text fw={600} size="sm" c="gray.7">Ward:</Text>
								<Text size="sm" c="dark.7">{ward}</Text>
							</Group>

							<Group gap="sm">
								<IconTimeline size={16} color="var(--mantine-color-gray-6)" />
								<Text fw={600} size="sm" c="gray.7">Progress:</Text>
								<Text size="sm" c="dark.7">{resolution?.description ? "100%" : "0%"}</Text>
							</Group>

							<Group gap="sm">
								<IconCalendar size={16} color="var(--mantine-color-gray-6)" />
								<Text fw={600} size="sm" c="gray.7">Reported on:</Text>
								<Text size="sm" c="dark.7">
									{reportedOn.toLocaleDateString(undefined, {
										year: 'numeric',
										month: 'long',
										day: 'numeric',
										weekday: 'short'
									})}
								</Text>
							</Group>
							<Group gap="sm">
								<IconUser size={16} color="var(--mantine-color-gray-6)" />
								<Text fw={600} size="sm" c="gray.7">Reported by:</Text>
								<Text size="sm" c="dark.7">{reportedBy}</Text>
							</Group>
							{resolution && (
								<Group gap="sm">
									<IconUser size={16}  />
									<Text fw={600} size="sm">Resolved by:</Text>
									<Text size="sm" c="dark.7">{resolution.resolvedBy}</Text>
								</Group>)}
						</SimpleGrid>
						<Stack gap="lg">
							{/* Enhanced map with loading state */}
							<Card withBorder radius="md" shadow="sm" p={0} style={{ overflow: 'hidden' }}>
								<Box p="sm" bg="gray.0" style={{ borderBottom: '1px solid var(--mantine-color-gray-3)' }}>
									<Group gap="xs">
										<IconMapPin size={16} color="var(--mantine-color-blue-6)" />
										<Text fw={600} size="sm" c="blue.7">Location</Text>
									</Group>
								</Box>
								<Box style={{ position: 'relative' }}>
									<AspectRatio ratio={16 / 9}>
										<iframe
											title="Issue location map"
											style={{ border: 0, width: '100%', height: '100%' }}
											loading="lazy"
											src={`https://www.google.com/maps?q=${location.lat},${location.lng}&z=17&output=embed`}
											onLoad={() => setMapLoaded(true)}
										/>
									</AspectRatio>
									{!mapLoaded && (
										<Skeleton height="100%" width="100%" style={{ position: 'absolute', top: 0 }} />
									)}
								</Box>
							</Card>

							{/* Enhanced AI review with better styling */}
							{resolution?.aiReview && (
								<Transition mounted={true} transition="slide-up" duration={300}>
									{(styles) => (
										<Alert
											icon={<IconInfoCircle size={18} />}
											variant="light"
											color={reviewColor}
											radius="md"
											style={styles}
											styles={{
												root: {
													borderWidth: 2,
												}
											}}
										>
											<Text fw={600} size="sm" mb={6}>
												AI Review of Completed Work
											</Text>
											<Text size="sm" style={{ lineHeight: 1.5 }}>
												{resolution.aiReview}
											</Text>
										</Alert>
									)}
								</Transition>
							)}
						</Stack>
					</Flex>
				</Paper>

				<Flex direction="row" gap={8} >
					<Grid gutter="xl" w={"50%"}>
						<Grid.Col span={"auto"}>
							<Stack gap="lg">
								<Card withBorder radius="md" p="md" bg="gray.0" mah={200}>
									<Group align="flex-start" gap="sm" mb="xs">
										<IconInfoCircle size={16} style={{ marginTop: 2, color: 'var(--mantine-color-blue-6)' }} />
										<Text fw={600} size="sm" c="blue.7">Problem Description:</Text>
									</Group>
									<Text size="sm" style={{ lineHeight: 1.6, overflow:"auto" }} pl="xl">
										{description}
									</Text>
								</Card>

								{/* Enhanced problem images with loading states and hover effects */}
								{problemImages.length > 0 && (
									<Box>
										<Group gap="xs" mb="md">
											<IconPhoto size={18} color="var(--mantine-color-blue-6)" />
											<Text fw={700} size="sm" c="blue.7">
												Problem Images
											</Text>
											<Badge size="xs" variant="outline" color="blue">
												{problemImages.length}
											</Badge>	
										</Group>
										<Group>
											{problemImages.map((url, i) => (
												<Box key={url} style={{ position: 'relative' }} w={300} h={300} >
													<Skeleton visible={!imageLoadStates[url]} radius="md">
														<Image
															src={url}
															alt={`Problem image ${i + 1}`}
															radius="md"
															fit="cover"
															h={300}
															style={{ 
																cursor: 'pointer',
																transition: 'transform 200ms ease, box-shadow 200ms ease',
																objectFit: 'fill'
															}}
															styles={{
																root: {
																	'&:hover': {
																		transform: 'scale(1.02)',
																		boxShadow: 'var(--mantine-shadow-md)',
																	}
																}
															}}
															onClick={() => launchViewer(problemImages, i)}
															onLoad={() => handleImageLoad(url)}
														/>
													</Skeleton>
													<Tooltip label="Click to view full size">
														<ActionIcon
															variant="filled"
															color="dark"
															size="sm"
															radius="xl"
															style={{
																position: 'absolute',
																top: 8,
																right: 8,
																opacity: 0.8,
															}}
															onClick={() => launchViewer(problemImages, i)}
														>
															<IconEye size={14} />
														</ActionIcon>
													</Tooltip>
												</Box>
											))}
										</Group>
									</Box>
								)}
							</Stack>
						</Grid.Col>
					</Grid>
					<Divider my="xl" orientation="vertical" mb={0} mt={0} p={0}/>
					<Grid gutter="xl" w={"50%"}>
						<Grid.Col span={"auto"}>
							{resolution && (
								<>
									<Stack gap="lg">
											<Card withBorder radius="md" p="md" bg="green.0" mah={200}>
												<Stack gap="md">
													<Box>
														<Text fw={600} size="sm" c="green.8" mb="xs">Resolution Description:</Text>
														<Text size="sm" style={{ lineHeight: 1.6, overflow:"auto" }} c="dark.7">
															{resolution.description}
														</Text>
													</Box>
												</Stack>
											</Card>

										{/* Enhanced resolution images */}
										{resolution.images.length > 0 && (
											<Box>
												<Group gap="xs" mb="md">
													<IconPhoto size={18} color="var(--mantine-color-green-6)" />
													<Text fw={700} size="sm" c="green.7">
														Resolution Images
													</Text>
													<Badge size="xs" variant="outline" color="green">
														{resolution.images.length}
													</Badge>
												</Group>
												<Group>
													{resolution.images.map((img, i) =>
														img.contentType.startsWith('image') ? (
															<Box key={img.url} style={{ position: 'relative' }} w={300} h={300}>
																<Skeleton visible={!imageLoadStates[img.url]} radius="md">
																	<Image
																		src={img.url}
																		alt={`Resolution image ${i + 1}`}
																		radius="md"
																		fit="cover"
																		h={300}
																		style={{ 
																			cursor: 'pointer',
																			transition: 'transform 200ms ease, box-shadow 200ms ease',
																			objectFit: 'fill'
																		}}
																		styles={{
																			root: {
																				'&:hover': {
																					transform: 'scale(1.02)',
																					boxShadow: 'var(--mantine-shadow-md)',
																				}
																			}
																		}}
																		onClick={() =>
																			launchViewer(
																				resolution.images.map(ii => ii.url),
																				i,
																			)
																		}
																		onLoad={() => handleImageLoad(img.url)}
																	/>
																</Skeleton>
																<Tooltip label="Click to view full size">
																	<ActionIcon
																		variant="filled"
																		color="dark"
																		size="sm"
																		radius="xl"
																		style={{
																			position: 'absolute',
																			top: 8,
																			right: 8,
																			opacity: 0.8,
																		}}
																		onClick={() =>
																			launchViewer(
																				resolution.images.map(ii => ii.url),
																				i,
																			)
																		}
																	>
																		<IconEye size={14} />
																	</ActionIcon>
																</Tooltip>
															</Box>
														) : null,
													)}
												</Group>
											</Box>
										)}
									</Stack>
								</>
							)}
						</Grid.Col>

						{/* <Grid.Col span={{ base: 12, md: 4 }}>
							{resolution && (<Stack gap="lg">
								<Card withBorder radius="md" shadow="sm" p={0} style={{ overflow: 'hidden' }}>
									<Box p="sm" bg="gray.0" style={{ borderBottom: '1px solid var(--mantine-color-gray-3)' }}>
										<Group gap="xs">
											<IconMapPin size={16} color="var(--mantine-color-blue-6)" />
											<Text fw={600} size="sm" c="blue.7">Location</Text>
										</Group>
									</Box>
									<Box style={{ position: 'relative' }}>
										<AspectRatio ratio={16 / 9}>
											<iframe
												title="Issue location map"
												style={{ border: 0, width: '100%', height: '100%' }}
												loading="lazy"
												src={`https://www.google.com/maps?q=${location.lat},${location.lng}&z=17&output=embed`}
												onLoad={() => setMapLoaded(true)}
											/>
										</AspectRatio>
										{!mapLoaded && (
											<Skeleton height="100%" width="100%" style={{ position: 'absolute', top: 0 }} />
										)}
									</Box>
								</Card>
							</Stack>)}
							
						</Grid.Col> */}
					</Grid>
				</Flex>

				{/* ───────── Enhanced sticky footer with loading state ───────── */}
				{showCloseIssueButton && (
					<Box
						pos="sticky"
						bottom={0}
						bg="var(--mantine-color-body)"
						px="lg"
						py="md"
						style={{
							borderTop: '2px solid var(--mantine-color-gray-2)',
							zIndex: 1,
							marginLeft: -24,
							marginRight: -24,
							marginBottom: -24,
							boxShadow: '0 -4px 12px rgba(0, 0, 0, 0.05)',
						}}
					>
						<Group justify="flex-end">
							<Button 
								size="md"
								loading={isClosingIssue}
								onClick={handleCloseIssue}
								style={{
									transition: 'all 200ms ease',
								}}
								leftSection={isClosingIssue ? null : <IconInfoCircle size={16} />}
							>
								{isClosingIssue ? 'Closing Issue...' : 'Close Issue'}
							</Button>
						</Group>
					</Box>
				)}

				{/* Loading overlay for the entire component when closing */}
			</Paper>

			{/* Enhanced viewer */}
			<GalleryViewer
				opened={viewerOpened}
				onClose={closeViewer}
				images={viewerImgs}
				initial={viewerStart}
			/>
		</>
	);
};