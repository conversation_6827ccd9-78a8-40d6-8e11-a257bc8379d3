import React, { useState } from 'react';
import { Modal, ActionIcon, Group, Image, Text } from '@mantine/core';
import { IconZoomIn, IconZoomOut, IconRotate, IconDownload } from '@tabler/icons-react';

interface MediaModalProps {
  opened: boolean;
  onClose: () => void;
  imageUrl: string;
  title?: string;
}

const MediaModal: React.FC<MediaModalProps> = ({ opened, onClose, imageUrl, title }) => {
  const [zoom, setZoom] = useState(1);

  const handleZoomIn = () => setZoom(prev => Math.min(prev + 0.25, 3));
  const handleZoomOut = () => setZoom(prev => Math.max(prev - 0.25, 0.25));
  const handleResetZoom = () => setZoom(1);

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `image-${Date.now()}.jpg`;
    link.click();
  };

  const styles = {
    container: {
      backgroundColor: '#ffffff',
      padding: '16px'
    },
    controlsBar: {
      backgroundColor: '#f3f4f6',
      padding: '8px',
      borderRadius: '8px',
      marginBottom: '12px'
    },
    zoomText: {
      color: '#111827',
      minWidth: '64px',
      textAlign: 'center' as const
    },
    imageContainer: {
      display: 'flex',
      justifyContent: 'center',
      overflow: 'auto',
      maxHeight: '384px'
    },
    image: {
      transform: `scale(${zoom})`,
      transition: 'transform 0.2s ease',
      maxWidth: 'none',
      cursor: 'grab'
    }
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      size="xl"
      title={title || "Media Viewer"}
      centered
      styles={{
        body: { padding: 0 },
        header: { backgroundColor: '#ffffff', color: '#1f2937', borderBottom: '1px solid #e5e7eb' },
      }}
    >
      <div style={styles.container}>
        <Group justify="center" mb="md" style={styles.controlsBar}>
          <ActionIcon
            variant="subtle"
            color="gray"
            onClick={handleZoomOut}
            disabled={zoom <= 0.25}
          >
            <IconZoomOut size={16} />
          </ActionIcon>

          <Text size="sm" style={styles.zoomText}>
            {Math.round(zoom * 100)}%
          </Text>

          <ActionIcon
            variant="subtle"
            color="gray"
            onClick={handleZoomIn}
            disabled={zoom >= 3}
          >
            <IconZoomIn size={16} />
          </ActionIcon>

          <ActionIcon variant="subtle" color="gray" onClick={handleResetZoom}>
            <IconRotate size={16} />
          </ActionIcon>

          <ActionIcon variant="subtle" color="gray" onClick={handleDownload}>
            <IconDownload size={16} />
          </ActionIcon>
        </Group>

        <div style={styles.imageContainer}>
          <Image
            src={imageUrl}
            alt="Media content"
            style={styles.image}
          />
        </div>
      </div>
    </Modal>
  );
};
export default MediaModal;