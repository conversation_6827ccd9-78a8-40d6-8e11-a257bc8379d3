import { useState, useEffect, memo, useCallback, useRef } from 'react';
import {
  Group,
  TextInput,
  Select,
  Pagination,
  Text,
  Box,
  ActionIcon,
  Tooltip,
} from '@mantine/core';
import { IconSearch, IconX, IconSortAscending, IconSortDescending } from '@tabler/icons-react';
import { useDebouncedValue } from '@mantine/hooks';

interface SearchAndPaginationProps {
  search: string;
  onSearchChange: (search: string) => void;
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  onPageChange: (page: number) => void;
  onLimitChange: (limit: number) => void;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  onSortChange: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
  sortOptions: Array<{ value: string; label: string }>;
  loading?: boolean;
}

export const SearchAndPagination = memo(function SearchAndPagination({
  search,
  onSearchChange,
  pagination,
  onPageChange,
  onLimitChange,
  sortBy,
  sortOrder,
  onSortChange,
  sortOptions,
  loading = false,
}: SearchAndPaginationProps) {
  const [localSearch, setLocalSearch] = useState(search);
  const [debouncedSearch] = useDebouncedValue(localSearch, 150);
  const [isUserTyping, setIsUserTyping] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const skipDebounceRef = useRef(false);

  // Sync localSearch with external search prop changes only when user is not actively typing
  useEffect(() => {
    if (!isUserTyping && search !== localSearch) {
      setLocalSearch(search);
    }
  }, [search, localSearch, isUserTyping]);

  // Apply debounced search to the parent component
  useEffect(() => {
    // Skip if the clear button was just pressed
    if (skipDebounceRef.current) {
      skipDebounceRef.current = false;
      return;
    }

    if (debouncedSearch !== search) {
      onSearchChange(debouncedSearch);
      
      // Clear any existing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      
      // Reset typing flag after a delay to ensure UI stability
      typingTimeoutRef.current = setTimeout(() => {
        setIsUserTyping(false);
      }, 500);
    }
  }, [debouncedSearch, onSearchChange, search]);

  // Clean up the timeout when component unmounts
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  const handleSortToggle = useCallback(() => {
    onSortChange(sortBy, sortOrder === 'asc' ? 'desc' : 'asc');
  }, [sortBy, sortOrder, onSortChange]);

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setIsUserTyping(true);
    setLocalSearch(e.target.value);
  }, []);

  const handleSearchClear = useCallback(() => {
    setIsUserTyping(true);
    setLocalSearch('');
    // Set flag to skip the debounce effect
    skipDebounceRef.current = true;
    // Immediately notify parent of empty search
    onSearchChange('');
    
    // Focus the input after clearing
    if (searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 0);
    }
  }, [onSearchChange]);

  const handleSortByChange = useCallback((value: string | null) => {
    if (value) {
      onSortChange(value, sortOrder);
    }
  }, [sortOrder, onSortChange]);

  const handleLimitChange = useCallback((value: string | null) => {
    if (value) {
      onLimitChange(parseInt(value));
    }
  }, [onLimitChange]);

  return (
    <Box>      <Group justify="space-between" mb="md">
        <Group wrap="nowrap" align="center">
          <TextInput
            placeholder="Search..."
            value={localSearch}
            onChange={handleSearchChange}
            leftSection={<IconSearch size={16} />}
            rightSection={
              localSearch ? (
                <ActionIcon
                  variant="subtle"
                  onClick={handleSearchClear}
                  disabled={loading}
                  tabIndex={-1} // Prevents focus stealing
                >
                  <IconX size={16} />
                </ActionIcon>
              ) : null
            }            style={{ width: 360 }}
            ref={searchInputRef}
            // Never disable search input to maintain focus during loading
          />          <Tooltip label={`Select field to sort by (current: ${sortOptions.find(opt => opt.value === sortBy)?.label || sortBy})`}>
            <Select
              placeholder="Sort by"
              value={sortBy}
              onChange={handleSortByChange}
              data={sortOptions}
              disabled={loading}
              style={{ width: 200 }}
              leftSection={sortOrder === 'asc' ? <IconSortAscending size={16} /> : <IconSortDescending size={16} />}
            />
          </Tooltip>
          
          <Tooltip label={`Toggle sort order: currently ${sortOrder === 'asc' ? 'Ascending (A→Z)' : 'Descending (Z→A)'}`}>
            <ActionIcon
              variant="light"
              onClick={handleSortToggle}
              disabled={loading}
            >
              {sortOrder === 'asc' ? <IconSortAscending size={16} /> : <IconSortDescending size={16} />}
            </ActionIcon>
          </Tooltip>
        </Group>
        <Select
          placeholder="Items per page"
          value={pagination?.limit.toString()}
          onChange={handleLimitChange}
          data={[
            { value: '5', label: '5 per page' },
            { value: '10', label: '10 per page' },
            { value: '20', label: '20 per page' },
            { value: '50', label: '50 per page' },
          ]}
          disabled={loading}
        />
      </Group>

      <Group justify="space-between" align="center" mt="md">
        <Text size="sm" c="dimmed">
          {pagination?.total === 0 
            ? 'No items found'
            : `Showing ${((pagination.page - 1) * pagination.limit) + 1}-${Math.min(pagination.page * pagination.limit, pagination.total)} of ${pagination.total} items`
          }
        </Text>
        
        {pagination.pages > 1 && (
          <Pagination
            value={pagination.page}
            onChange={onPageChange}
            total={pagination.pages}
            disabled={loading}
          />
        )}
      </Group>
    </Box>
  );
}, (prevProps, nextProps) => {
  // Custom comparison to prevent unnecessary re-renders
  return (
    prevProps.search === nextProps.search &&
    prevProps.sortBy === nextProps.sortBy &&
    prevProps.sortOrder === nextProps.sortOrder &&
    prevProps.loading === nextProps.loading &&
    prevProps.pagination.page === nextProps.pagination.page &&
    prevProps.pagination.limit === nextProps.pagination.limit &&
    prevProps.pagination.total === nextProps.pagination.total &&
    prevProps.pagination.pages === nextProps.pagination.pages &&
    prevProps.onSearchChange === nextProps.onSearchChange &&
    prevProps.onPageChange === nextProps.onPageChange &&
    prevProps.onLimitChange === nextProps.onLimitChange &&
    prevProps.onSortChange === nextProps.onSortChange
  );
});
