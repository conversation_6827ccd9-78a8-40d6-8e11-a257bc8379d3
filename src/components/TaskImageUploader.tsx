import { useState } from "react";
import { apiClient } from "../config/axios";
import {
  Button,
  Group,
  Text,
  Paper,
  FileInput,
  Stack,
  Image,
  Loader,
  Checkbox,
  Title,
  Divider,
  Card,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Textarea,
  Modal
} from "@mantine/core";
import { notifications } from "@mantine/notifications";
import {
  IconUpload,
  IconPhoto,
  IconCheck,
  IconX,
  IconEdit,

} from "@tabler/icons-react";
import { SuggestedTask, ImageUploadResponse } from "../types";

interface TaskImageUploaderProps {
  meetingId: string;
  onTasksCreated: () => void;
}

export default function TaskImageUploader({ meetingId, onTasksCreated }: TaskImageUploaderProps) {
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [uploadResponse, setUploadResponse] = useState<ImageUploadResponse | null>(null);
  const [selectedTasks, setSelectedTasks] = useState<Record<number, boolean>>({});
  const [editingTask, setEditingTask] = useState<{ index: number; task: SuggestedTask } | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const handleFileChange = (file: File | null) => {
    setFile(file);
    // Reset states when a new file is selected
    setUploadResponse(null);
    setSelectedTasks({});
  };

  const handleUpload = async () => {
    if (!file) {
      notifications.show({
        title: "Error",
        message: "Please select an image to upload",
        color: "red",
        icon: <IconX />
      });
      return;
    }

    setLoading(true);

    try {
      const formData = new FormData();
      formData.append("image", file);

      const response = await apiClient.post<ImageUploadResponse>(
        `/api/meetings/${meetingId}/tasks/upload`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data"
          }
        }
      );

      setUploadResponse(response.data);
      
      // Initialize all tasks as selected
      const initialSelectedState: Record<number, boolean> = {};
      response.data.suggestedTasks.forEach((_, index) => {
        initialSelectedState[index] = true;
      });
      setSelectedTasks(initialSelectedState);

      notifications.show({
        title: "Success",
        message: "Image uploaded and tasks extracted",
        color: "green",
        icon: <IconCheck />
      });
    } catch (error) {
      console.error("Error uploading image:", error);
      notifications.show({
        title: "Error",
        message: "Failed to upload image or extract tasks",
        color: "red",
        icon: <IconX />
      });
    } finally {
      setLoading(false);
    }
  };

  const handleTaskSelection = (index: number, checked: boolean) => {
    setSelectedTasks({
      ...selectedTasks,
      [index]: checked
    });
  };

  const handleEditTask = (index: number, task: SuggestedTask) => {
    setEditingTask({ index, task: { ...task } });
    setIsEditModalOpen(true);
  };

  const handleSaveEdit = () => {
    if (editingTask) {
      const { index, task } = editingTask;
      if (uploadResponse) {
        const updatedTasks = [...uploadResponse.suggestedTasks];
        updatedTasks[index] = task;
        setUploadResponse({
          ...uploadResponse,
          suggestedTasks: updatedTasks
        });
      }
      setIsEditModalOpen(false);
      setEditingTask(null);
    }
  };

  const handleSaveTasks = async () => {
    if (!uploadResponse) return;

    setLoading(true);

    try {
      const selectedTasksArray = uploadResponse.suggestedTasks.filter(
        (_, index) => selectedTasks[index]
      );

      if (selectedTasksArray.length === 0) {
        notifications.show({
          title: "Warning",
          message: "No tasks selected to save",
          color: "yellow"
        });
        setLoading(false);
        return;
      }

      await apiClient.post(`/api/meetings/${meetingId}/tasks/from-image`, {
        tasks: selectedTasksArray,
        imageUrl: uploadResponse.imageUrl,
        originalText: uploadResponse.extractedText
      });

      notifications.show({
        title: "Success",
        message: `${selectedTasksArray.length} tasks created successfully`,
        color: "green",
        icon: <IconCheck />
      });

      // Reset states
      setFile(null);
      setUploadResponse(null);
      setSelectedTasks({});
      
      // Notify parent component to refresh tasks
      onTasksCreated();
    } catch (error) {
      console.error("Error saving tasks:", error);
      notifications.show({
        title: "Error",
        message: "Failed to save tasks",
        color: "red",
        icon: <IconX />
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Stack>
      <Paper p="md" withBorder>
        <Stack>
          <Title order={4}>Upload MOM</Title>
          <Text size="sm" c="dimmed">
            Take a photo of your handwritten notes and upload it to extract tasks
          </Text>
          
          <Group>
            <FileInput
              placeholder="Select image"
              accept="image/*"
              value={file}
              onChange={handleFileChange}
              leftSection={<IconPhoto size={14} />}
              style={{ flex: 1 }}
              clearable
            />
            <Button
              onClick={handleUpload}
              leftSection={<IconUpload size={14} />}
              loading={loading}
              disabled={!file}
            >
              Upload
            </Button>
          </Group>
        </Stack>
      </Paper>

      {loading && !uploadResponse && (
        <Paper p="xl" withBorder>
          <Stack align="center" py="md">
            <Loader size="md" />
            <Text>Processing image and extracting tasks...</Text>
          </Stack>
        </Paper>
      )}

      {uploadResponse && (
        <Paper p="md" withBorder>
          <Stack>
            <Group justify="space-between">
              <Title order={4}>Extracted Tasks</Title>
              <Button onClick={handleSaveTasks} disabled={loading}>
                Save Selected Tasks
              </Button>
            </Group>

            <Group align="flex-start">
              <Stack style={{ flex: 1 }}>
                <Text fw={500}>Original Image</Text>
                <Image
                  src={`http://localhost:3000${uploadResponse.imageUrl}`}
                  radius="sm"
                  alt="Uploaded handwritten notes"
                  style={{ maxWidth: "100%", maxHeight: "300px" }}
                />
              </Stack>

              <Stack style={{ flex: 1 }}>
                <Text fw={500}>Extracted Text</Text>
                <Paper p="xs" withBorder style={{ whiteSpace: "pre-wrap" }}>
                  {uploadResponse.extractedText}
                </Paper>
              </Stack>
            </Group>

            <Divider my="sm" />

            <Text fw={500}>Review and Edit Tasks</Text>
            <Stack>
              {uploadResponse.suggestedTasks.map((task, index) => (
                <Card key={index} withBorder shadow="sm" padding="sm">
                  <Group justify="space-between" mb="xs">
                    <Checkbox
                      checked={selectedTasks[index] || false}
                      onChange={(e) => handleTaskSelection(index, e.currentTarget.checked)}
                      label={<Text fw={500}>{task.title}</Text>}
                    />
                    <Group>
                      <ActionIcon
                        color="blue"
                        variant="subtle"
                        onClick={() => handleEditTask(index, task)}
                      >
                        <IconEdit size={16} />
                      </ActionIcon>
                    </Group>
                  </Group>

                  {task.description && (
                    <Text size="sm" c="dimmed" mb="xs">
                      {task.description}
                    </Text>
                  )}

                  <Group>
                    <Badge color={task.priority === "high" ? "red" : task.priority === "medium" ? "yellow" : "blue"}>
                      {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
                    </Badge>
                    <Badge color={task.status === "completed" ? "green" : "blue"}>
                      {task.status.charAt(0).toUpperCase() + task.status.slice(1)}
                    </Badge>
                  </Group>
                </Card>
              ))}
            </Stack>
          </Stack>
        </Paper>
      )}

      {/* Edit Task Modal */}
      <Modal
        opened={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="Edit Task"
        size="md"
      >
        {editingTask && (
          <Stack>
            <TextInput
              label="Title"
              value={editingTask.task.title}
              onChange={(e) =>
                setEditingTask({
                  ...editingTask,
                  task: { ...editingTask.task, title: e.target.value }
                })
              }
              required
            />
            <Textarea
              label="Description"
              value={editingTask.task.description}
              onChange={(e) =>
                setEditingTask({
                  ...editingTask,
                  task: { ...editingTask.task, description: e.target.value }
                })
              }
              minRows={3}
              resize="vertical"
            />
            <Select
              label="Priority"
              value={editingTask.task.priority}
              onChange={(value) =>
                setEditingTask({
                  ...editingTask,
                  task: {
                    ...editingTask.task,
                    priority: value as "low" | "medium" | "high"
                  }
                })
              }
              data={[
                { value: "low", label: "Low" },
                { value: "medium", label: "Medium" },
                { value: "high", label: "High" }
              ]}
            />
            <Select
              label="Status"
              value={editingTask.task.status}
              onChange={(value) =>
                setEditingTask({
                  ...editingTask,
                  task: {
                    ...editingTask.task,
                    status: value as "pending" | "completed"
                  }
                })
              }
              data={[
                { value: "pending", label: "Pending" },
                { value: "completed", label: "Completed" }
              ]}
            />
            <Group justify="flex-end" mt="md">
              <Button variant="light" onClick={() => setIsEditModalOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveEdit}>Save Changes</Button>
            </Group>
          </Stack>
        )}
      </Modal>
    </Stack>
  );
}
