import { useState, useEffect, useCallback } from 'react';
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Paper,
  TextInput,
  Modal,
  Stack,
  MultiSelect,
  Select,
  Textarea,  Card,
  ActionIcon,
  Grid,
  List,
  Alert,
  Skeleton,
}from '@mantine/core';
import { DateInput } from '@mantine/dates';
import { notifications } from '@mantine/notifications';
import { IconPlus, IconEdit, IconTrash, IconCalendar, IconUser, IconUsers } from '@tabler/icons-react';
import { apiClient } from '../config/axios';
import { Ward, Directory } from '../types';
import { useNavigate } from 'react-router-dom';
import { usePagination } from '../hooks/usePagination';
import { SearchAndPagination } from '../components/SearchAndPagination';

export default function WardsPage() {
  const navigate = useNavigate();
  const [directories, setDirectories] = useState<Directory[]>([]);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [editingWardId, setEditingWardId] = useState<string | null>(null);
  const [deletingWard, setDeletingWard] = useState<Ward | null>(null);
  const [formData, setFormData] = useState<{
    name: string,
    description: string,
    resolver: string,
    zone: string,
    district?: string
  }>({
    name: '',
    description: '',
    resolver: '',
    zone: '',
    district: ''
  });
  // Fetch function for pagination
  const fetchWards = useCallback(async (params: any): Promise<any> => {
    try{
      const response = await apiClient.get('/ward', { params });
      console.log(response.data.responseObject);
      return response.data.responseObject;
    } catch(er){
      notifications.show({
        title: "Error",
        message: "Failed to load wards",
        color: "red",
      })
    }
  }, []);

  const {
    data: wards,
    pagination,
    loading,
    search,
    sortBy,
    sortOrder,
    setPage,
    setLimit,
    setSearch,
    setSorting,
    refresh,
  } = usePagination<Ward>(fetchWards, {
    sortBy: 'name',
    sortOrder: 'asc'
  });

  const sortOptions = [
    { value: 'name', label: 'Ward Name' },
    { value: 'meetingFrequency', label: 'Meeting Frequency' },
    { value: 'startDate', label: 'Start Date' },
    { value: 'createdAt', label: 'Created Date' },
  ];

  useEffect(() => {
    fetchDirectories();
  }, []);

  const fetchDirectories = useCallback(async () => {
    try {
      // Fetch all directories for the form dropdowns
      const response = await apiClient.get('/directory', { 
        params: { limit: 1000 } // Get all directories for the form
      });
      console.log(response.data.responseObject, directories);
      setDirectories(response.data.responseObject.data);
    } catch (error) {
      console.error('Failed to fetch directories:', error);
    }
  }, []);

  const handleSubmitWard = async (isEdit: boolean = false) => {
    try {
      // Validate required fields
      if (!formData.name.trim()) {
        notifications.show({
          title: 'Validation Error',
          message: 'Ward name is required',
          color: 'red',
        });
        return;
      }     

      if (!formData.description) {
        notifications.show({
          title: 'Validation Error',
          message: 'Ward zone is required',
          color: 'red',
        });
        return;
      }

      if (!formData.zone) {
        notifications.show({
          title: 'Validation Error',
          message: 'Ward zone is required',
          color: 'red',
        });
        return;
      }

     const payload = {
        ...(formData.name?.trim() && { title: formData.name }),
        ...(formData.description?.trim() && { description: formData.description }),
        ...(formData.resolver?.trim() && { resolver: [formData.resolver] }),
        ...(formData.zone?.trim() && { zone: formData.zone }),
        ...(formData.district?.trim() && { district: formData.district }),
      };

      if (isEdit && editingWardId) {
        await apiClient.patch(`/ward/${editingWardId}`, payload);
      } else {
        await apiClient.post('/ward', payload);
      }

      notifications.show({
        title: `Ward ${isEdit ? 'Updated' : 'Created'}`,
        message: `Ward successfully ${isEdit ? 'updated' : 'created'}`,
        color: 'green',
      });

      refresh();
      setIsCreateModalOpen(false);
      resetForm();
    } catch (error) {
      console.error('Failed to save ward:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to save ward',
        color: 'red',
      });
    }
  };

  const handleDeleteWard = (ward: Ward) => {
    setDeletingWard(ward);
    setIsDeleteModalOpen(true);
  };

  const confirmDeleteWard = async () => {
    if (!deletingWard) return;

    try {
      await apiClient.delete(`/api/ward/${deletingWard._id}`);
      notifications.show({
        title: 'Success',
        message: 'Ward deleted successfully',
        color: 'green',
      });
      refresh();
      setIsDeleteModalOpen(false);
      setDeletingWard(null);
    } catch (error) {
      console.error('Failed to delete ward:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to delete ward',
        color: 'red',
      });
    }
  };

  const handleEditWard = async (id: string) => {
    const ward = wards.find(c => c._id === id);
    if (ward) {
      setFormData({
        name: ward.title,
        description: ward.description || '',
        resolver: ward.resolver && ward.resolver[0]?._id || '',
        zone: ward.zone || '',
        district: ward.district || ''
      });
      setEditingWardId(id);
      setIsCreateModalOpen(true);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      resolver: '',
      zone: '',
      district: '',
    });
    setEditingWardId(null);
  };

  const getAssistantName = (assistant?: { _id: string; userName: string }) => {
    return assistant?.userName || 'Not assigned';
  };
  return (
    <Container size="xl" py="md">      <Group justify="space-between" mb="lg">
        <div>
          <Title order={1}>Wards</Title>
          <Text c="dimmed">Manage your wards and their tasks</Text>
        </div>
        <Button
          leftSection={<IconPlus size={16} />}
          onClick={() => setIsCreateModalOpen(true)}
        >
          Create Ward
        </Button>
      </Group>

      <Paper p="md" withBorder mb="lg">
        <SearchAndPagination
          search={search}
          onSearchChange={setSearch}
          pagination={pagination}
          onPageChange={setPage}
          onLimitChange={setLimit}
          sortBy={sortBy}
          sortOrder={sortOrder}
          onSortChange={setSorting}
          sortOptions={sortOptions}
          loading={loading}
        />
      </Paper>      {loading ? (
        <Grid gutter="md">
          {Array.from({ length: 6 }).map((_, index) => (
            <Grid.Col key={`skeleton-${index}`} span={{ base: 12, md: 6, lg: 4 }}>
              <Card withBorder shadow="sm" padding="lg">
                <Group align="flex-start" justify="space-between" mb="md" wrap="nowrap">
                  <Skeleton height={24} width="70%" />
                  <Group gap="xs" wrap="nowrap">
                    <Skeleton height={28} width={28} circle />
                    <Skeleton height={28} width={28} circle />
                  </Group>
                </Group>
                
                <Skeleton height={16} mb="lg" width="90%" />
                
                <Stack gap="md" mb="lg">
                  <Group gap="xs">
                    <Skeleton height={16} width={16} circle />
                    <Skeleton height={16} width="30%" />
                    <Skeleton height={16} width="50%" />
                  </Group>
                  
                  <Group gap="xs">
                    <Skeleton height={16} width={16} circle />
                    <Skeleton height={16} width="40%" />
                    <Skeleton height={16} width="40%" />
                  </Group>
                  
                  <Group gap="xs">
                    <Skeleton height={16} width={16} circle />
                    <Skeleton height={16} width="45%" />
                    <Skeleton height={16} width="35%" />
                  </Group>
                </Stack>
                
                <Skeleton height={36} width="100%" mt="md" />
              </Card>
            </Grid.Col>
          ))}
        </Grid>
      ) : wards.length === 0 ? (
        <Paper p="xl" withBorder>
          <Stack align="center">
            <Text c="dimmed">No wards found</Text>
            <Button
              variant="light"
              onClick={() => setIsCreateModalOpen(true)}
            >
              Create your first ward
            </Button>
          </Stack>
        </Paper>
      ) : (        <Grid gutter="md">
          {wards.map((ward) => (
            <Grid.Col key={ward._id} span={{ base: 12, md: 6, lg: 4 }}>
              <Card withBorder shadow="sm">
                <Group align="flex-start" justify="space-between" mb="md" wrap="nowrap">
                  <Text fw={500} size="lg">
                    {ward.title}
                  </Text>
                  <Group gap="xs" wrap="nowrap">                    
                    <ActionIcon
                      variant="light"
                      color="blue"
                      onClick={() => handleEditWard(ward._id)}
                    >
                      <IconEdit size={16} />
                    </ActionIcon>
                    <ActionIcon
                      variant="light"
                      color="red"
                      onClick={() => handleDeleteWard(ward)}
                    >
                      <IconTrash size={16} />
                    </ActionIcon>
                  </Group>
                </Group>

                {ward.description && (
                  <Text size="sm" c="dimmed" mb="md">
                    {ward.description}
                  </Text>
                )}

                <Stack gap="xs">

                  <Group gap="xs">
                    <IconUser size={16} stroke={1.5} />
                    <Text size="sm" fw={500}>Resolver:</Text>
                    <Text size="sm">{getAssistantName(ward.resolver && ward.resolver[0] ? ward.resolver[0] : undefined)}</Text>
                  </Group>
                </Stack>
              </Card>
            </Grid.Col>
          ))}
        </Grid>
      )}

      <Modal
        opened={isCreateModalOpen}
        onClose={() => {
          setIsCreateModalOpen(false);
          resetForm();
        }}
        title={editingWardId ? "Edit Ward" : "Create Ward"}
        size="lg"
      >
        <Stack>
          <TextInput
            label="Ward Name"
            placeholder="Enter ward name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            required
          />

          <Textarea
            label="Description"
            placeholder="Enter ward description"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            minRows={3}
            required
            resize="vertical"
          />
          
          <Select 
            label="Resolver"
            placeholder='select resolver'
            value={formData.resolver}
            onChange={(value) => setFormData({ ...formData, resolver: value || '' })}
            data={directories.map((item) => (
               { label: item.userName,
                value: item._id
              }
              ))}
            searchable
            description={"Select resolver for the ward"}
          />

          <TextInput
            label="Zone"
            placeholder="Enter zone"
            value={formData.zone}
            onChange={(e) => setFormData({ ...formData, zone: e.target.value })}
            required
            disabled={editingWardId ? true : false}
          />

          <TextInput
            label="District"
            placeholder="Enter district name"
            value={formData.district}
            onChange={(e) => setFormData({ ...formData, district: e.target.value })}
            disabled={editingWardId ? true : false}
          />

          <Group justify="flex-end" mt="md">
            <Button variant="light" onClick={() => {
              setIsCreateModalOpen(false);
              resetForm();
            }}>
              Cancel
            </Button>
            <Button onClick={() => handleSubmitWard(!!editingWardId)}>
              {editingWardId ? 'Update' : 'Create'}
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        opened={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setDeletingWard(null);
        }}
        title="Delete Ward"
        size="md"
      >
        <Stack>
          <Alert color="red" title="Warning: This action cannot be undone">
            You are about to permanently delete the ward <strong>"{deletingWard?.title}"</strong>.
          </Alert>

          <Text size="sm" fw={500} mb="xs">This will permanently delete:</Text>
          <List size="sm" spacing="xs">
            <List.Item>The ward and all its information</List.Item>
            <List.Item>All historical data and progress tracking</List.Item>
          </List>

          <Text size="sm" c="dimmed" mt="md">
            Ward resolver will not be deleted from the directory, but their association with this ward will be lost.
          </Text>

          <Group justify="flex-end" mt="lg">
            <Button
              variant="light"
              onClick={() => {
                setIsDeleteModalOpen(false);
                setDeletingWard(null);
              }}
            >
              Cancel
            </Button>
            <Button
              color="red"
              onClick={confirmDeleteWard}
            >
              Delete Ward
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  );
}
