import { useCallback, useEffect, useState } from 'react';
import apiClient from '@/config/axios';
import { ProblemDetails } from '@/components/ComplaintDetails';
import { useParams } from 'react-router-dom';
import { Complaint } from '@/types';
import { LoadingOverlay } from '@mantine/core';
import { notifications } from '@mantine/notifications';

export default function ComplaintDetails() {
    const {  id } = useParams();
    const [loading, setLoading] = useState(true);
    const [complaintData, setComplaintData] = useState<Complaint | undefined>(undefined);
    const fetchComplaintData = useCallback(async(id:string) => {
        try{
            setLoading(true);
            console.log(id);
            const data = await apiClient.get(`/complaints/${id}`);
            setComplaintData(data.data.responseObject);
            console.log(data);
        } catch(err){
            console.log(err);
        } finally {
            setLoading(false);
        }
    }, []);

    const closeIssue = useCallback(async() => {
        if (!complaintData?._id) {
            return;
        }
        try {
            await apiClient.patch(`/complaints/${complaintData._id}/complete`);
            fetchComplaintData(complaintData._id);
            notifications.show({
                title: "Issue closed",
                message: "Issue is closed successfully",
                color: "green",
                
            });
        } catch (error) {

            notifications.show({
                title: "Error while closing issue.",
                message: `Error while closing issue, please try again after some time`,
                color: "red",
            });
            console.error()
        }
    }, [fetchComplaintData, complaintData?._id]);

    useEffect(() => {
        if (id) {
            fetchComplaintData(id);
        }
    }, [fetchComplaintData, id]);

    if (loading) {
		return <LoadingOverlay visible={true} overlayProps={{ radius: "md", blur: 1 }} />
    }
    if (!complaintData) {
        return <div>Undfined</div>
    }
    return (
        <div>
            <ProblemDetails
                id={complaintData._id}
                title={complaintData.title}
                description={complaintData?.description || ""}
                location={{
                    lat: complaintData.location.coordinates[1],
                    lng: complaintData.location.coordinates[0],
                }}
                problemImages={complaintData.mediaFiles?.map((item) => item.url) || []}
                reportedBy={complaintData.createdBy?.userName}
                reportedOn={new Date()}
                status={complaintData.complaintStatus}
                ward={complaintData.location.locationMetaData.ward.title} 
                resolution={complaintData.resolution?.length?{
                    description: complaintData.resolution[complaintData.resolution.length -1].description ?? "",
                    images: complaintData.resolution[complaintData.resolution.length - 1].mediaFiles ?? [],
                    resolvedBy: complaintData.resolvedBy?.userName ?? "",
                    aiReview: complaintData.aiReviewOfResolution,
                }:undefined}
                showCloseIssueButton={complaintData.issueCloseAllowed}
                onCloseIssue={closeIssue}
            />
        </div>
    )
}
