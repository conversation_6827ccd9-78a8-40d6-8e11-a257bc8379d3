import { useCallback } from 'react';
import { 
    Container, 
    Title, 
    Text, 
    Paper, 
    Table, 
    Group, 
    Loader, 
    Stack} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import apiClient from '@/config/axios';
import { usePagination } from '../hooks/usePagination';
import { SearchAndPagination } from '../components/SearchAndPagination';
import { location } from '@/types';
import { Link } from 'react-router-dom';

interface Work {
    _id: string;
    title: string;
    problemCategories: {
        problemType: number;
        title: string;
    };
    location: location;
    createdAt: string;
    updatedAt: string;
}

export default function Work() {
    const fetchWork = useCallback(async (params: any): Promise<any> => {
        try {
            const response = await apiClient.get('/work', { params });
            console.log('Work response:', response.data);
            
            if (response.data.responseObject) {
                return response.data.responseObject;
            }
        } catch (error) {
            console.error('Failed to fetch work items:', error);
            notifications.show({
                title: 'Error',
                message: 'Failed to load work items',
                color: 'red'
            });
            return { data: [], pagination: { page: 1, limit: 10, total: 0, pages: 0 } };
        }
    }, []);

    const {
        data: workItems,
        pagination,
        loading,
        search,
        sortBy,
        sortOrder,
        setPage,
        setLimit,
        setSearch,
        setSorting,
    } = usePagination<Work>(fetchWork, {
        sortBy: 'createdAt',
        sortOrder: 'desc'
    });

    const sortOptions = [
        { value: 'title', label: 'Title' },
        { value: 'problemCategories.title', label: 'Problem Category' },
        { value: 'createdAt', label: 'Created At' },
        { value: 'updatedAt', label: 'Updated At' }
    ];

    // Create table rows from work data
    const rows = workItems.map((item, index) => (
        <Table.Tr key={item._id}>
            <Table.Td>{(pagination.page - 1) * pagination.limit + index + 1}</Table.Td>
            <Table.Td>
                <Link to={`/works/${item._id}`} style={{textDecoration: "none"}}>{item.title}</Link>
            </Table.Td>
            <Table.Td>{item.location.locationMetaData.ward.title}</Table.Td>
            <Table.Td>{new Date(item.createdAt).toLocaleDateString()}</Table.Td>
            <Table.Td>{new Date(item.updatedAt).toLocaleDateString()}</Table.Td>
        </Table.Tr>
    ));

    return (
        <Container size="xl" py="md">
            <Group justify="space-between" mb="lg">
                <div>
                    <Title>Works</Title>
                    <Text c="dimmed">Track Works</Text>
                </div>
            </Group>
            {/* Search and Pagination Component */}
            <Paper p="md" withBorder mb="lg">
                <SearchAndPagination
                    search={search}
                    onSearchChange={setSearch}
                    pagination={pagination}
                    onPageChange={setPage}
                    onLimitChange={setLimit}
                    sortBy={sortBy}
                    sortOrder={sortOrder}
                    onSortChange={setSorting}
                    sortOptions={sortOptions}
                    loading={loading}
                />
            </Paper>

            {/* Work Items Table */}
            {loading ? (
                <Paper p="xl" withBorder>
                    <Stack align="center">
                        <Loader />
                        <Text>Loading work items...</Text>
                    </Stack>
                </Paper>
            ) : workItems.length === 0 ? (
                <Paper p="xl" withBorder>
                    <Stack align="center" gap="md" py={40}>
                        <Text size="xl" fw={500} c="dimmed">No Work Items Found</Text>
                    </Stack>
                </Paper>
            ) : (
                <Paper withBorder>
                    <Table horizontalSpacing="md" verticalSpacing="xs" miw={700} layout="fixed">
                        <Table.Thead>
                            <Table.Tr>
                                <Table.Th style={{ width: '60px' }}>
                                    S.No.
                                </Table.Th>
                                <Table.Th>
                                    Title
                                </Table.Th>
                                <Table.Th>
                                    Ward
                                </Table.Th>
                                <Table.Th>
                                    Created At
                                </Table.Th>
                                <Table.Th>
                                    Updated At
                                </Table.Th>
                            </Table.Tr>
                        </Table.Thead>
                        <Table.Tbody>
                            {rows}
                        </Table.Tbody>
                    </Table>
                </Paper>
            )}
        </Container>
    );
}
