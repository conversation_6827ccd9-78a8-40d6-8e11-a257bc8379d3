import { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Paper,
  TextInput,
  Modal,
  Stack,
  Textarea,
  Card,
  ActionIcon,
  Grid,
  Loader,
  List,
  Alert,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { notifications } from '@mantine/notifications';
import { IconPlus, IconTrash, IconCalendar, IconListCheck, IconEdit } from '@tabler/icons-react';
import { apiClient } from '../config/axios';
import { Meeting, Ward } from '../types';
import { usePagination } from '../hooks/usePagination';
import { SearchAndPagination } from '../components/SearchAndPagination';

export default function MeetingsPage() {
  const navigate = useNavigate();
  const { wardId } = useParams();
  const [wards, setWards] = useState<Ward[]>([]);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [deletingMeeting, setDeletingMeeting] = useState<Meeting | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    ward: '',
    agenda: '',
    startDate: new Date() as Date | null,
  });
  const [editingMeetingId, setEditingMeetingId] = useState<string | null>(null);
  // Fetch function for pagination
  const fetchMeetings = useCallback(async (params: any) => {
    if (!wardId) {
      throw new Error('Ward ID is required');
    }
    const response = await apiClient.get(`/api/ward/${wardId}/meetings`, { params });
    return response.data;
  }, [wardId]);
  const {
    data: meetings,
    pagination,
    loading,
    search,
    sortBy,
    sortOrder,
    setPage,
    setLimit,
    setSearch,
    setSorting,
    refresh,
  } = usePagination<Meeting>(fetchMeetings, {
    sortBy: 'startDate',
    sortOrder: 'desc'
  });

  const sortOptions = [
    { value: 'startDate', label: 'Meeting Date' },
    { value: 'title', label: 'Title' },
    { value: 'createdAt', label: 'Created Date' },
  ];

  useEffect(() => {
    if (wardId) {
      fetchWards();
      // Pre-select ward if coming from ward context
      if (formData.ward === '') {
        setFormData(prev => ({ ...prev, ward: wardId }));
      }
    }
  }, [wardId]);
  const fetchWards = async () => {
    try {
      // Fetch all wards for the form dropdown
      const response = await apiClient.get('/api/ward', { 
        params: { limit: 1000 } // Get all wards for the form
      });
      setWards(response.data.data || response.data);
    } catch (error) {
      console.error('Failed to fetch wards:', error);
    }
  };

  const handleDeleteMeeting = (meeting: Meeting) => {
    setDeletingMeeting(meeting);
    setIsDeleteModalOpen(true);
  };

  const confirmDeleteMeeting = async () => {
    if (!deletingMeeting || !wardId) return;

    try {
      await apiClient.delete(`/api/ward/${wardId}/meetings/${deletingMeeting._id}`);
      notifications.show({
        title: 'Success',
        message: 'Meeting deleted successfully',
        color: 'green',
      });
      refresh();
      setIsDeleteModalOpen(false);
      setDeletingMeeting(null);
    } catch (error) {
      console.error('Failed to delete meeting:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to delete meeting',
        color: 'red',
      });
    }
  };

  const handleViewTasks = (meetingId: string) => {
    navigate(`/ward/${wardId}/meetings/${meetingId}`);
  };

  const handleEditMeeting = (meeting: Meeting) => {
    setFormData({
      title: meeting.title,
      ward: wardId || '',
      agenda: meeting.agenda || '',
      startDate: new Date(meeting.startDate),
    });
    setEditingMeetingId(meeting._id);
    setIsCreateModalOpen(true);
  };

  const handleSubmitMeeting = async () => {
    if (!formData.title) {
      return void notifications.show({
        title: 'Validation Error',
        message: 'Meeting title is required',
        color: 'red',
      });
    }
    if (!formData.startDate) {
      return void notifications.show({
        title: 'Validation Error',
        message: 'Meeting start date is required',
        color: 'red',
      });
    }
    try {
      if (!wardId) {
        throw new Error('Ward ID is required');
      }

      if (editingMeetingId) {
        await apiClient.patch(`/api/meetings/${editingMeetingId}`, {
          ...formData,
          startDate: formData.startDate?.toISOString() || new Date().toISOString(),
        });
        notifications.show({
          title: 'Success',
          message: 'Meeting updated successfully',
          color: 'green',
        });
      } else {
        await apiClient.post(`/api/ward/${wardId}/meetings`, {
          ...formData,
          startDate: formData.startDate?.toISOString() || new Date().toISOString(),
        });
        notifications.show({
          title: 'Success',
          message: 'Meeting scheduled successfully',
          color: 'green',
        });
      }

      refresh();
      setIsCreateModalOpen(false);
      resetForm();
    } catch (error) {
      console.error('Failed to save meeting:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to save meeting',
        color: 'red',
      });
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      ward: wardId || '',
      agenda: '',
      startDate: new Date(),
    });
    setEditingMeetingId(null);
  };



  // Early return if ward ID is missing
  if (!wardId) {
    return (
      <Container size="xl" py="md">
        <Text c="red">Error: Ward ID is required. Please access meetings through a ward.</Text>
      </Container>
    );
  }

  return (
    <Container size="xl" py="md">
      <Group justify="space-between" mb="lg">
        <div>
          <Title>Meetings</Title>
          <Text c="dimmed">Schedule and manage ward meetings</Text>
        </div>        <Button
          leftSection={<IconPlus size={14} />}
          onClick={() => setIsCreateModalOpen(true)}
        >
          Schedule Meeting
        </Button>
      </Group>

      <Paper p="md" withBorder mb="lg">
        <SearchAndPagination
          search={search}
          onSearchChange={setSearch}
          pagination={pagination}
          onPageChange={setPage}
          onLimitChange={setLimit}
          sortBy={sortBy}
          sortOrder={sortOrder}
          onSortChange={setSorting}
          sortOptions={sortOptions}
          loading={loading}
        />
      </Paper>

      {loading ? (
        <Paper p="xl" withBorder>
          <Stack align="center">
            <Loader />
            <Text>Loading meetings...</Text>
          </Stack>
        </Paper>
      ) : meetings.length === 0 ? (
        <Paper p="xl" withBorder>
          <Stack align="center" gap="md" py={40}>
            <IconCalendar size={48} stroke={1.5} color="var(--mantine-color-gray-3)" />
            <div style={{ textAlign: 'center' }}>
              <Text size="xl" fw={500} c="dimmed">No Meetings Scheduled</Text>
              <Text size="sm" c="dimmed" mt={4}>
                Click "Schedule Meeting" to create your first meeting
              </Text>
            </div>
            <Button
              variant="light"
              leftSection={<IconPlus size={14} />}
              onClick={() => setIsCreateModalOpen(true)}
              mt="sm"
            >
              Schedule Meeting
            </Button>
          </Stack>
        </Paper>
      ) : (
        <Grid>          {meetings.map((meeting) => {
            const m = meeting as Meeting;
            return (
            <Grid.Col key={m._id} span={{ base: 12, md: 6, lg: 4 }}>
              <Card withBorder shadow="sm">
                <Group align="flex-start" justify="space-between" mb="md" wrap="nowrap">
                  <Text fw={500} size="lg">
                    {m.title}
                  </Text>
                  <Group gap="xs" wrap="nowrap">
                    <ActionIcon
                      variant="light"
                      color="blue"
                      onClick={() => handleEditMeeting(m)}
                    >
                      <IconEdit size={16} />
                    </ActionIcon>
                    <ActionIcon
                      variant="light"
                      color="red"
                      onClick={() => handleDeleteMeeting(m)}
                    >
                      <IconTrash size={16} />
                    </ActionIcon>
                  </Group>
                </Group>

                {m.agenda && (
                  <Text size="sm" c="dimmed" mb="md">
                    {m.agenda}
                  </Text>
                )}

                <Group gap="xs">
                  <IconCalendar size={16} />
                  <Text size="sm" fw={500}>Start Date:</Text>
                  <Text size="sm">
                    {new Date(m.startDate).toLocaleDateString()}
                  </Text>
                </Group>

                <Button
                  variant="light"
                  fullWidth
                  mt="md"
                  leftSection={<IconListCheck size={16} />}
                  onClick={() => handleViewTasks(m._id)}
                >
                  View Tasks
                </Button>
              </Card>
            </Grid.Col>
          );})}
        </Grid>
      )}

      {/* Create Meeting Modal */}
      <Modal
        opened={isCreateModalOpen}
        onClose={() => {
          setIsCreateModalOpen(false);
          resetForm();
        }}
        title={editingMeetingId ? "Edit Meeting" : "Schedule Ward Meeting"}
        size="lg"
      >
        <Stack>
          {wards.length > 0 && (
            <Text size="sm" c="dimmed" mb="md">
              {editingMeetingId ? "Editing" : "Creating"} meeting for: <strong>{wards.find(c => c._id === wardId)?.name || 'Ward'}</strong>
            </Text>
          )}

          <TextInput
            label="Meeting Title"
            placeholder="Enter meeting title"
            value={formData.title}
            onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            required
          />



          <Textarea
            label="Agenda"
            placeholder="Enter meeting agenda"
            resize="vertical"
            value={formData.agenda}
            onChange={(e) => setFormData({ ...formData, agenda: e.target.value })}
          />

          <DatePickerInput
            label="Start Date"
            placeholder="Pick date and time"
            value={formData.startDate}
            onChange={(value) => setFormData({ ...formData, startDate: value ? new Date(value) : null })}
            required
          />

          <Group justify="flex-end" mt="md">
            <Button variant="light" onClick={() => {
              setIsCreateModalOpen(false);
              resetForm();
            }}>
              Cancel
            </Button>
            <Button onClick={handleSubmitMeeting}>{editingMeetingId ? 'Update Meeting' : 'Schedule Meeting'}</Button>
          </Group>
        </Stack>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        opened={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setDeletingMeeting(null);
        }}
        title="Delete Meeting"
        size="md"
      >
        <Stack>
          <Alert color="red" title="Warning: This action cannot be undone">
            You are about to permanently delete the meeting <strong>"{deletingMeeting?.title}"</strong>.
          </Alert>

          <Text size="sm" fw={500} mb="xs">This will permanently delete:</Text>
          <List size="sm" spacing="xs">
            <List.Item>The meeting and all its information</List.Item>
            <List.Item>All tasks associated with this meeting</List.Item>
            <List.Item>All task assignments and progress tracking</List.Item>
            <List.Item>The meeting agenda and notes</List.Item>
            <List.Item>All attendee records for this meeting</List.Item>
            <List.Item>Any uploaded images or documents</List.Item>
          </List>

          <Text size="sm" c="dimmed" mt="md">
            This will not affect other meetings or the ward itself.
          </Text>

          <Group justify="flex-end" mt="lg">
            <Button
              variant="light"
              onClick={() => {
                setIsDeleteModalOpen(false);
                setDeletingMeeting(null);
              }}
            >
              Cancel
            </Button>
            <Button
              color="red"
              onClick={confirmDeleteMeeting}
            >
              Delete Meeting
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  );
}
