import { Container, Title, Text, Button, Group, Stack } from '@mantine/core';
import { Link } from 'react-router-dom';

const NotFoundPage = () => {
  return (
    <Container size={600} style={{ paddingTop: 150, paddingBottom: 80, textAlign: 'center' }} >
      <Stack align="center" gap={"sm"} justify='center'>
        <Title order={1} size={80} fw={900} c="dimmed">
          404
        </Title>
        <Title order={2}>Page not found</Title>
        <Text c="dimmed" size="lg">
          Sorry, the page you are looking for doesn't exist or has been moved.
        </Text>
        <Group mt="md">
          <Link to="/">
            <Button variant="filled" color="blue" size="md">
              Go back to dashboard
            </Button>
          </Link>
        </Group>
      </Stack>
    </Container>
  );
};

export default NotFoundPage;