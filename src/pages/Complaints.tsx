import { Group, Table, Text, Select, Container, Title, Paper, Badge, Loader, Stack, Collapse, Divider } from "@mantine/core";
import { useCallback, useEffect, useState } from "react";
import apiClient from "@/config/axios";
import { Complaint, Directory, ComplaintStatus, complaintStatus } from "@/types";
import { usePagination } from "../hooks/usePagination";
import { SearchAndPagination } from "../components/SearchAndPagination";
import { notifications } from "@mantine/notifications";
import { Link } from "react-router-dom";
import { IconChevronDown, IconChevronUp, IconFilter } from "@tabler/icons-react";

function getComplaintStatusText(status: ComplaintStatus): string {
    switch (status) {
        case complaintStatus.unresolved:
            return 'Unresolved';
        case complaintStatus.sendForReview:
            return 'Sent for Review';
        case complaintStatus.aiReviewGenerated:
            return 'AI Review Generated';
        case complaintStatus.completed:
            return 'Completed';
        default:
            return 'Unknown Status';
    }
}

export default function Complaints() {
    const [directories, setDirectories] = useState<Directory[]>([]);
    const [filterValue, setFilterValue] = useState<string>('');
    const [statusFilter, setStatusFilter] = useState<string>('');
    const [filterOpen, setFilterOpen] = useState<boolean>(false);
    
    const fetchComplaints = useCallback(async (params: any): Promise<any> => {
        try {
            params.filter = {};
            if (filterValue) {
                params.filter.assignedTo = filterValue; 
            }
            
            if (statusFilter) {
                params.filter.complaintStatus = statusFilter;
            }
            
            const response = await apiClient.get('/complaints', { params });
            console.log('Complaints response:', response.data);
            
            if (response.data.responseObject) {
                return response.data.responseObject;
            }
            
            return {
                data: response.data,
                pagination: {
                    page: params.page || 1,
                    limit: params.limit || 10,
                    total: response.data.length,
                    pages: Math.ceil(response.data.length / (params.limit || 10))
                }
            };
        } catch (error) {
            console.error('Failed to fetch complaints:', error);
            notifications.show({
                title: 'Error',
                message: 'Failed to load complaints',
                color: 'red'
            });
            return { data: [], pagination: { page: 1, limit: 10, total: 0, pages: 0 } };
        }
    }, [filterValue, statusFilter]);

    // Use the pagination hook
    const {
        data: complaints,
        pagination,
        loading,
        search,
        sortBy,
        sortOrder,
        setPage,
        setLimit,
        setSearch,
        setSorting,
        refresh
    } = usePagination<Complaint>(fetchComplaints, {
        sortBy: 'title',
        sortOrder: 'asc'
    });

    const sortOptions = [
        { value: 'title', label: 'Title' },
        { value: 'complaintStatus', label: 'Status' },
        { value: 'createdAt', label: 'Created Date' }
    ];

    // Fetch directories for the filter dropdown
    const fetchDirectories = useCallback(async (): Promise<void> => {
        try {
            const apiResponse = await apiClient.get('/directory', { 
                params: { 
                    page: 1,
                    limit: 100,
                    search: "",
                    sortBy: "",
                    sortOrder: ""
                } 
            });
            
            if (apiResponse.data.responseObject && Array.isArray(apiResponse.data.responseObject.data)) {
                setDirectories(apiResponse.data.responseObject.data);
            } else if (Array.isArray(apiResponse.data.responseObject)) {
                setDirectories(apiResponse.data.responseObject);
            } else {
                console.error('Unexpected directory response format:', apiResponse.data);
                setDirectories([]);
            }
        } catch (error) {
            console.error('Failed to fetch directories:', error);
            setDirectories([]);
        }
    }, []);

    useEffect(() => {
        fetchDirectories();
    }, [fetchDirectories]);

    // When filter changes, refresh the complaints
    useEffect(() => {
        refresh();
    }, [filterValue, statusFilter, refresh]);

    const directoryOptions = directories && Array.isArray(directories) 
        ? directories.map((item) => ({
            label: item.userName || 'Unknown',
            value: item._id,
        }))
        : [];

    const rows = complaints.map((row, idx) => (
        <Table.Tr key={row._id}>
            <Table.Td>{pagination ? (pagination.page - 1) * pagination.limit + idx + 1 : idx + 1}.</Table.Td>
            <Table.Td>
                <Link to={`/complaints/${row._id}`} style={{textDecoration: "none"}}> {row.title} </Link>
            </Table.Td>
            <Table.Td>{row.location.locationMetaData.ward.title}</Table.Td>
            <Table.Td>
                <Badge color={getStatusColor(row.complaintStatus)}>
                    {getComplaintStatusText(row.complaintStatus)}
                </Badge>
            </Table.Td>
            <Table.Td>{row.resolvedBy?.userName || "Not resolved yet"}</Table.Td>
        </Table.Tr>
    ));

    // Helper function to get status color
    function getStatusColor(status: number): string {
        switch (status) {
            case complaintStatus.unresolved:
                return 'red';
            case complaintStatus.sendForReview:
                return 'blue';
            case complaintStatus.aiReviewGenerated:
                return 'blue';
            case complaintStatus.completed:
                return 'green';
            default :
                return 'gray';
        }
    }

    // Status options for filter dropdown
    const statusOptions = [
        { value: complaintStatus.unresolved.toString(), label: 'Unresolved' },
        { value: complaintStatus.sendForReview.toString(), label: 'Pending For Review' },
        { value: complaintStatus.aiReviewGenerated.toString(), label: 'Reviewed By AI' },
        { value: complaintStatus.completed.toString(), label: 'Completed' },
    ];

    return (
        <Container size="xl" py="md">
            <Group justify="space-between" mb="lg">
                <div>
                <Title>Complaints</Title>
                <Text c="dimmed">Track complaints</Text>
                </div>
            </Group>
            <Paper p="xs" withBorder mb="sm">
                <Group justify="space-between" onClick={() => setFilterOpen((prev) => !prev)}>
                    <Group gap={"xs"} p={"sm"} align="center">
                        <IconFilter size={16}/>
                        <Text size="sm">Filters</Text>
                    </Group>
                    {filterOpen ? <IconChevronUp size={16}/> : <IconChevronDown size={16} /> }
                </Group>
                <Collapse in={filterOpen}>
                    <Divider/>
                    <Group p="md">
                            <div>
                                <Text size="sm"> Assignee</Text>
                                <Select 
                                    value={filterValue}
                                    data={directoryOptions}
                                    onChange={(value) => {
                                        console.log('Filter changed:', value);
                                        setFilterValue(value || "");
                                    }}
                                    clearable
                                    placeholder="All assignees"
                                />
                            </div>
                            <div>
                                <Text size="sm">Status</Text>
                                <Select 
                                    value={statusFilter}
                                    data={statusOptions}
                                    onChange={(value) => {
                                        console.log('Status filter changed:', value);
                                        setStatusFilter(value || "");
                                    }}
                                    clearable
                                    placeholder="All statuses"
                                />
                            </div>
                    </Group>
                </Collapse>
            </Paper>
            <Paper p="md" withBorder mb="lg">
                <Group justify="space-between" mb="md" align="start">
                    <div style={{marginTop: "20px"}}>
                        <SearchAndPagination
                            search={search}
                            onSearchChange={setSearch}
                            pagination={pagination}
                            onPageChange={setPage}
                            onLimitChange={setLimit}
                            sortBy={sortBy}
                            sortOrder={sortOrder}
                            onSortChange={setSorting}
                            sortOptions={sortOptions}
                            loading={loading}
                        />
                    </div>
                </Group>
            </Paper>

            {/* Complaints Table */}
            {loading ? (
                <Paper p="xl" withBorder>
                    <Stack align="center">
                        <Loader />
                        <Text>Loading complaints...</Text>
                    </Stack>
                </Paper>
            ) : complaints.length === 0 ? (
                <Paper p="xl" withBorder>
                    <Stack align="center" gap="md" py={40}>
                        <Text size="xl" fw={500} c="dimmed">No Complaints Found</Text>
                    </Stack>
                </Paper>
            ) : (
                <Paper withBorder>
                    <Table horizontalSpacing="md" verticalSpacing="xs" miw={700} layout="fixed" highlightOnHover>
                        <Table.Thead>
                            <Table.Tr>
                                <Table.Th style={{ width: "70px"}}>S No.</Table.Th>
                                <Table.Th>
                                    Title
                                </Table.Th>
                                <Table.Th>
                                    Ward
                                </Table.Th>
                                <Table.Th>
                                    Status
                                </Table.Th>
                                <Table.Th>
                                    Resolved By
                                </Table.Th>
                            </Table.Tr>
                        </Table.Thead>
                        <Table.Tbody>
                            {rows}
                        </Table.Tbody>
                    </Table>
                </Paper>
            )}
        </Container>
    );
}
