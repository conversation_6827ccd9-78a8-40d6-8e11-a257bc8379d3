import {
  Container,
  Title,
  Text,
  Button,
  Group,
  TextInput,
  Textarea,
  Select,
  Modal,
  Tabs,
  Stack,
  MultiSelect,
  Paper,
  Badge,
  Menu,
  ActionIcon,

  SegmentedControl,
  Progress,
  SimpleGrid,
  ScrollArea,
} from '@mantine/core';
import { showNotification } from '@mantine/notifications';
import { DateInput } from '@mantine/dates';
import { IconPlus, IconEdit, IconTrash, IconDotsVertical, IconCheck, IconAlarm, IconUsers, IconUpload } from '@tabler/icons-react';
import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { apiClient } from '../config/axios';
import { Meeting, Task } from '../types';
import TaskImageUploader from '../components/TaskImageUploader';

interface TaskFormData {
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  assignedTo: string[];
  status: 'pending' | 'completed';
  deadline: Date | null;
  meetingId: string;
}

export default function MeetingDetails() {
  const { wardId, meetingId } = useParams();
  const navigate = useNavigate();
  const [meeting, setMeeting] = useState<Meeting | null>(null);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [isCreateTaskModalOpen, setIsCreateTaskModalOpen] = useState(false);
  const [isEditTaskModalOpen, setIsEditTaskModalOpen] = useState(false);
  const [isUploaderOpen, setIsUploaderOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'completed'>('all');
  const [priorityFilter, setPriorityFilter] = useState<'all' | 'low' | 'medium' | 'high'>('all');
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [taskForm, setTaskForm] = useState<TaskFormData>({
    title: '',
    description: '',
    priority: 'medium',
    assignedTo: [],
    status: 'pending',
    deadline: null,
    meetingId: '',
  });

  useEffect(() => {
    if (wardId && meetingId) {
      fetchMeetingDetails();
    }
  }, [wardId, meetingId]);

  const fetchMeetingDetails = async () => {
    try {
      setLoading(true);      // Fetch meeting details and tasks separately for better reliability
      const [meetingRes, tasksRes] = await Promise.all([
        apiClient.get(`/api/meetings/${meetingId}`),
        apiClient.get(`/api/meetings/${meetingId}/tasks`, {
          params: { limit: 1000 } // Get all tasks for this meeting
        })
      ]);

      setMeeting(meetingRes.data);
      // Handle both paginated and non-paginated response formats
      const tasksData = tasksRes.data.data || tasksRes.data;
      setTasks(Array.isArray(tasksData) ? tasksData : []);
    } catch (error) {
      console.error('Failed to fetch meeting details:', error);
      showNotification({
        title: 'Error',
        message: 'Failed to load meeting details',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };
  const handleCreateTask = async () => {
    try {
      if (!taskForm.title || !taskForm.priority || !taskForm.status) {
        showNotification({
          title: 'Error',
          message: 'Please fill in all required fields (Title, Priority, and Status)',
          color: 'red',
        });
        return;
      }

      await apiClient.post(`/api/meetings/${meetingId}/tasks`, {
        ...taskForm,
        deadline: taskForm.deadline?.toISOString() || null,
      });

      showNotification({
        title: 'Success',
        message: 'Task created successfully',
        color: 'green',
      });

      setIsCreateTaskModalOpen(false);
      fetchMeetingDetails();
      resetTaskForm();
    } catch (error) {
      console.error('Failed to create task:', error);
      showNotification({
        title: 'Error',
        message: 'Failed to create task',
        color: 'red',
      });
    }
  };

  const handleUpdateTaskStatus = async (taskId: string, newStatus: 'pending' | 'completed') => {
    try {
      await apiClient.patch(`/api/meetings/${meetingId}/tasks/${taskId}`, {
        status: newStatus
      });

      setTasks(tasks.map(task =>
        task._id === taskId ? { ...task, status: newStatus } : task
      ));

      showNotification({
        title: 'Success',
        message: 'Task status updated',
        color: 'green',
      });
    } catch (error) {
      console.error('Failed to update task status:', error);
      showNotification({
        title: 'Error',
        message: 'Failed to update task status',
        color: 'red',
      });
    }
  };

  const handleEditTask = (task: Task) => {
    setEditingTask(task);
    setTaskForm({
      title: task.title,
      description: task.description || '',
      priority: task.priority,
      assignedTo: task.assignedTo?.map(user => (typeof user === 'string' ? user : user._id)) || [],
      status: task.status,
      deadline: task.deadline ? new Date(task.deadline) : null,
      meetingId: task.meetingId,
    });
    setIsEditTaskModalOpen(true);
  };
  const handleUpdateTask = async () => {
    try {
      if (!editingTask?._id) return;
      
      if (!taskForm.title || !taskForm.priority || !taskForm.status) {
        showNotification({
          title: 'Error',
          message: 'Please fill in all required fields (Title, Priority, and Status)',
          color: 'red',
        });
        return;
      }

      await apiClient.patch(`/api/meetings/${meetingId}/tasks/${editingTask._id}`, {
        ...taskForm,
        deadline: taskForm.deadline?.toISOString() || null,
      });

      showNotification({
        title: 'Success',
        message: 'Task updated successfully',
        color: 'green',
      });

      setIsEditTaskModalOpen(false);
      fetchMeetingDetails();
      resetTaskForm();
    } catch (error) {
      console.error('Failed to update task:', error);
      showNotification({
        title: 'Error',
        message: 'Failed to update task',
        color: 'red',
      });
    }
  };

  const handleDeleteTask = async (taskId: string) => {
    try {
      await apiClient.delete(`/api/meetings/${meetingId}/tasks/${taskId}`);
      setTasks(tasks.filter(task => task._id !== taskId));
      showNotification({
        title: 'Success',
        message: 'Task deleted successfully',
        color: 'green',
      });
    } catch (error) {
      console.error('Failed to delete task:', error);
      showNotification({
        title: 'Error',
        message: 'Failed to delete task',
        color: 'red',
      });
    }
  };

  const filteredTasks = tasks.filter(task => {
    const matchesStatus = statusFilter === 'all' || task.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || task.priority === priorityFilter;
    return matchesStatus && matchesPriority;
  });



  const taskStats = {
    total: tasks.length,
    completed: tasks.filter(t => t.status === 'completed').length,
    pending: tasks.filter(t => t.status === 'pending').length,
    high: tasks.filter(t => t.priority === 'high').length,
  };

  const resetTaskForm = () => {
    setTaskForm({
      title: '',
      description: '',
      priority: 'medium',
      assignedTo: [],
      status: 'pending',
      deadline: new Date(),
      meetingId: '',
    });
    setEditingTask(null);
  };

  // Early return if required params are missing
  if (!wardId || !meetingId) {
    return (
      <Container size="xl" py="md">
        <Stack align="center" justify="center" h={400} gap="md">
          <Text c="red" size="lg">Error: Ward ID and Meeting ID are required</Text>
          <Button 
            variant="light"
            onClick={() => navigate(-1)}
          >
            Go Back
          </Button>
        </Stack>
      </Container>
    );
  }

  return (
    <Container size="xl" py="md">
      <Group justify="space-between" mb="lg">
        <div>
          <Title order={1}>{meeting?.title}</Title>
          <Text c="dimmed">Meeting Tasks and Agenda</Text>
        </div>
        <Group>
          <Button
            variant="outline"
            leftSection={<IconUpload size={16} />}
            onClick={() => setIsUploaderOpen(true)}
          >
            Upload MOM
          </Button>
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={() => setIsCreateTaskModalOpen(true)}
          >
            Create Task
          </Button>
        </Group>
      </Group>

      <Modal
        opened={isUploaderOpen}
        onClose={() => setIsUploaderOpen(false)}
        title="Upload MOM"
        size="xl"
        styles={{
          header: {
            backgroundColor: 'var(--mantine-color-body)',
            borderBottom: '1px solid var(--mantine-color-gray-3)',
            position: 'sticky',
            top: 0,
            zIndex: 1000
          },
          body: {
            padding: 0
          },
          content: {
            overflowY: 'hidden'
          }
        }}
      >
        <ScrollArea.Autosize mah="calc(90vh - 80px)" p="md">
          <TaskImageUploader 
            meetingId={meetingId!} 
            onTasksCreated={() => {
              setIsUploaderOpen(false);
              fetchMeetingDetails();
            }} 
          />
        </ScrollArea.Autosize>
      </Modal>      <Tabs defaultValue="tasks">
        <Tabs.List>
          <Tabs.Tab value="tasks">Tasks</Tabs.Tab>
          <Tabs.Tab value="agenda">Agenda</Tabs.Tab>
        </Tabs.List>        <Tabs.Panel value="tasks" pt="md">
          {loading ? (
            <Stack align="center" justify="center" h="60vh">
              <Text>Loading tasks...</Text>
            </Stack>
          ) : tasks.length === 0 ? (
            <Stack align="center" justify="center" h="60vh" gap="md">
              <Text size="xl" fw={500} c="dimmed" ta="center">No tasks have been created yet</Text>
              <Text size="sm" c="dimmed" ta="center" maw={400}>
                Click 'Create Task' button to add a new task or use 'Upload MOM' to extract tasks from your meeting minutes.
              </Text>
            </Stack>
          ) : (
            <Stack>
              {/* Task Stats */}
              <SimpleGrid cols={4}>
                <Paper withBorder p="md">
                  <Stack>
                    <Text size="sm" c="dimmed">Total Tasks</Text>
                    <Text fw={700} size="xl">{taskStats.total}</Text>
                  </Stack>
                </Paper>
                <Paper withBorder p="md">
                  <Stack>
                    <Text size="sm" c="dimmed">Completed</Text>
                    <Text fw={700} size="xl" c="green">{taskStats.completed}</Text>
                  </Stack>
                </Paper>
                <Paper withBorder p="md">
                  <Stack>
                    <Text size="sm" c="dimmed">Pending</Text>
                    <Text fw={700} size="xl" c="blue">{taskStats.pending}</Text>
                  </Stack>
                </Paper>
                <Paper withBorder p="md">
                  <Stack>
                    <Text size="sm" c="dimmed">High Priority</Text>
                    <Text fw={700} size="xl" c="red">{taskStats.high}</Text>
                  </Stack>
                </Paper>
              </SimpleGrid>

              {/* Progress Bar */}
              <Paper withBorder p="md">
                <Stack>
                  <Text size="sm" fw={500}>Progress</Text>
                  <Progress 
                    value={tasks.length ? (taskStats.completed / taskStats.total) * 100 : 0}
                    color="green"
                    size="lg"
                  />
                  <Text size="xs" c="dimmed">
                    {taskStats.completed} of {taskStats.total} tasks completed
                  </Text>
                </Stack>
              </Paper>

              {/* Filters */}
              <Group>              <SegmentedControl
                  data={[
                    { label: 'All', value: 'all' },
                    { label: 'Pending', value: 'pending' },
                    { label: 'Completed', value: 'completed' }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value as 'all' | 'pending' | 'completed')}
                />
                <SegmentedControl
                  data={[
                    { label: 'All', value: 'all' },
                    { label: 'High', value: 'high' },
                    { label: 'Medium', value: 'medium' },
                    { label: 'Low', value: 'low' }
                  ]}
                  value={priorityFilter}
                  onChange={(value) => setPriorityFilter(value as 'all' | 'low' | 'medium' | 'high')}
                />            </Group>

              {/* Task List */}              {filteredTasks.length === 0 ? (
                <Stack align="center" justify="center" h="20vh">
                  <Text c="dimmed" ta="center">No tasks match the selected filters.</Text>
                </Stack>
              ) : (
                <Stack>
                  {filteredTasks.map((task) => (
                    <Paper key={task._id} p="md" withBorder>
                      <Group justify="space-between" mb="xs">
                        <Group>
                          <ActionIcon 
                            variant={task.status === 'completed' ? 'filled' : 'light'}
                            color={task.status === 'completed' ? 'green' : 'gray'}
                            title={task.status === 'completed' ? 'Mark as Pending' : 'Mark as Completed'}
                            onClick={() => handleUpdateTaskStatus(task._id, task.status === 'completed' ? 'pending' : 'completed')}
                          >
                            <IconCheck size={16} />
                          </ActionIcon>                          <Text fw={500}>
                            {task.title}
                          </Text>
                        </Group>
                        <Group>
                          <Badge color={task.priority === 'high' ? 'red' : task.priority === 'medium' ? 'yellow' : 'blue'}>
                            {task.priority}
                          </Badge>
                          <Badge color={task.status === 'completed' ? 'green' : 'blue'}>
                            {task.status}
                          </Badge>
                          <Menu position="bottom-end">
                            <Menu.Target>
                              <ActionIcon>
                                <IconDotsVertical size={16} />
                              </ActionIcon>
                            </Menu.Target>
                            <Menu.Dropdown>
                              <Menu.Item 
                                leftSection={<IconEdit size={14} />}
                                onClick={() => handleEditTask(task)}
                              >
                                Edit
                              </Menu.Item>
                              <Menu.Item 
                                color="red"                               leftSection={<IconTrash size={14} />}
                                onClick={() => handleDeleteTask(task._id)}
                              >
                                Delete
                              </Menu.Item>
                            </Menu.Dropdown>
                          </Menu>
                        </Group>
                      </Group>
                      {task.description && (
                        <Text size="sm" c="dimmed" mb="xs">{task.description}</Text>
                      )}
                      <Group gap="lg">
                        {task.assignedTo && task.assignedTo.length > 0 && (
                          <Group gap="xs">
                            <IconUsers size={16} stroke={1.5} />
                            <Text size="sm">
                              {task.assignedTo.map(person => person.name).join(', ')}
                            </Text>
                          </Group>
                        )}
                        {task.deadline && (
                          <Group gap="xs">
                            <IconAlarm size={16} stroke={1.5} />
                            <Text size="sm">
                              {new Date(task.deadline).toLocaleDateString()}
                            </Text>
                          </Group>
                        )}
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              )}
            </Stack>
          )}
        </Tabs.Panel>

        <Tabs.Panel value="agenda" pt="md">
          {meeting?.agenda && (
            <Paper p="md" withBorder>
              <Text style={{ whiteSpace: 'pre-wrap' }}>{meeting.agenda}</Text>
            </Paper>
          )}
        </Tabs.Panel>
      </Tabs>

      {/* Create/Edit Task Modal */}
      <Modal
        opened={isCreateTaskModalOpen || isEditTaskModalOpen}
        onClose={() => {
          setIsCreateTaskModalOpen(false);
          setIsEditTaskModalOpen(false);
          resetTaskForm();
        }}
        title={editingTask ? 'Edit Task' : 'Create New Task'}
      >
        <Stack>
          <TextInput
            label="Title"
            placeholder="Enter task title"
            value={taskForm.title}
            onChange={(e) => setTaskForm({ ...taskForm, title: e.target.value })}
            required
          />

          <Textarea
            label="Description"
            placeholder="Enter task description"
            value={taskForm.description}
            onChange={(e) => setTaskForm({ ...taskForm, description: e.target.value })}
            minRows={3}
            resize="vertical"
          />

          <Select
            label="Priority"
            value={taskForm.priority}
            onChange={(value) => setTaskForm({ ...taskForm, priority: value as 'low' | 'medium' | 'high' })}
            data={[
              { value: 'low', label: 'Low' },
              { value: 'medium', label: 'Medium' },
              { value: 'high', label: 'High' }
            ]}
            required
          />

          <Select
            label="Status"
            value={taskForm.status}
            onChange={(value) => setTaskForm({ ...taskForm, status: value as 'pending' | 'completed' })}
            data={[
              { value: 'pending', label: 'Pending' },
              { value: 'completed', label: 'Completed' }
            ]}
            required
          />

          <MultiSelect
            label="Assign To"
            placeholder="Select team members"
            value={taskForm.assignedTo}
            onChange={(value) => setTaskForm({ ...taskForm, assignedTo: value })}
            style={{ display: "none" }}
            data={(meeting?.attendees ?? []).map(attendee => ({
              value: attendee._id,
              label: attendee.name
            }))}
          />

          <DateInput
            label="Deadline"
            placeholder="Select a deadline"
            value={taskForm.deadline}
            onChange={(date) => setTaskForm({ ...taskForm, deadline: date ? new Date(date) : null })}
            clearable
          />

          <Button onClick={editingTask ? handleUpdateTask : handleCreateTask}>
            {editingTask ? 'Update Task' : 'Create Task'}
          </Button>
        </Stack>
      </Modal>
    </Container>
  );
}
