import { useState, useCallback } from 'react';
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Paper,
  TextInput,
  Textarea,
  Modal,
  Stack,
  Card,
  ActionIcon,
  Grid,
  Loader,
  List,
  Alert,
  Select,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { IconPlus, IconEdit, IconTrash, IconMail, IconPhone, IconMapPin } from '@tabler/icons-react';
import { apiClient } from '../config/axios';
import { Directory } from '../types';
import { usePagination } from '../hooks/usePagination';
import { SearchAndPagination } from '../components/SearchAndPagination';

// Move DirectoryForm outside to prevent recreation on every render
const DirectoryForm = ({
  formData,
  setFormData,
  onSubmit,
  onCancel,
  title,
  directories,
}: {
  formData: any;
  setFormData: (data: any) => void;
  onSubmit: () => void;
  onCancel: () => void;
  title: string;
  directories: Directory[];
}) => (
  <Stack>
    <TextInput
      label="Designation"
      placeholder="Enter designation"
      value={formData.designation}
      onChange={(e) => setFormData({ ...formData, designation: e.target.value })}
      required
    />

    <TextInput
      label="Name"
      placeholder="Enter full name"
      value={formData.name}
      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
      required
    />

    <Textarea
      label="Description"
      placeholder="Enter description"
      value={formData.description}
      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
      minRows={2}
      resize="vertical"
    />

    <TextInput
      label="Email"
      placeholder="Enter email address"
      value={formData.email}
      onChange={(e) => setFormData({ ...formData, email: e.target.value })}
      required
    />

    <TextInput
      label="Phone"
      placeholder="Enter phone number"
      value={formData.phone}
      onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
      required
    />

    <Textarea
      label="Address"
      placeholder="Enter address"
      value={formData.address}
      onChange={(e) => setFormData({ ...formData, address: e.target.value })}
      minRows={2}
      resize="vertical"
    />

    <Select 
      label="Reporting Manager"
      placeholder="Select reporting manager"
      value={formData.reportingManager}
      onChange={(value) => setFormData({ ...formData, reportingManager: value })}
      data={directories.map((item) => ({label: item.userName, value: item._id}))}
    />

    <Group justify="flex-end" mt="md">
      <Button variant="light" onClick={onCancel}>
        Cancel
      </Button>
      <Button onClick={onSubmit}>{title}</Button>
    </Group>
  </Stack>
);

export default function DirectoryPage() {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [currentDirectory, setCurrentDirectory] = useState<Directory | null>(null);
  const [deletingDirectory, setDeletingDirectory] = useState<Directory | null>(null);
  const [directoryUsage, setDirectoryUsage] = useState<{
    committees: string[];
    isAssistant: string[];
  } | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    designation: '',
    description: '',
    email: '',
    phone: '',
    address: '',
    reportingManager: ''
  });
  // Fetch function for pagination
  const fetchDirectories = useCallback(async (params: any): Promise<any> => {
    const apiResponse = await apiClient.get('/directory', { params });
    console.log(apiResponse.data.responseObject);
    // const response = {
    //   data: [
    //     {
    //       _id: "6858e6703e5de81bc942a510",
    //       name: "Officer pankaj",
    //       designation: "officer",
    //       description: "description of officer pankaj",
    //       email: "<EMAIL>",
    //       phone: "8580822170",
    //       address: "sector 91 mohali\nmohali punjab",
    //       createdBy: {
    //         _id: "6858e480f64bb683f3b6c40d",
    //         email: "<EMAIL>",
    //         password: "$2b$08$BFkA7IINndY.6Sm25o5dXeTj69SVwnf/SIv3YJCnzPHiDbrzXh40C",
    //         name: "Administrator",
    //         role: 1,
    //         createdAt: "2025-06-23T05:22:08.012Z",
    //         updatedAt: "2025-06-23T05:22:08.012Z",
    //         __v: 0
    //       },
    //       createdAt: "2025-06-23T05:30:24.133Z",
    //       __v: 0
    //     }
    //   ],
    //   pagination: {
    //     page: 1,
    //     limit: 10,
    //     total: 1,
    //     pages: 1
    //   }
    // };
    return apiResponse.data.responseObject;
  }, []);

  const {
    data: directories,
    pagination,
    loading,
    search,
    sortBy,
    sortOrder,
    setPage,
    setLimit,
    setSearch,
    setSorting,
    refresh,
  } = usePagination<Directory>(fetchDirectories, {    sortBy: 'name',
    sortOrder: 'asc'
  });

  const sortOptions = [
    { value: 'name', label: 'Name' },
    { value: 'designation', label: 'Designation' },
    { value: 'email', label: 'Email' },
    { value: 'createdAt', label: 'Created Date' },
  ];

  const validateDirectory = () => {
    if (!formData.designation) {
      notifications.show({
        title: 'Validation Error',
        message: 'Designation is required',
        color: 'red',
      });
      return false;
    }
    if (!formData.name) {
      notifications.show({
        title: 'Validation Error',
        message: 'Member name is required',
        color: 'red',
      });
      return false;
    }
    if (!formData.email) {
      notifications.show({
        title: 'Validation Error',
        message: 'Member email is required',
        color: 'red',
      });
      return false;
    }
    return true;
  };

  const handleCreateDirectory = async () => {
    if (!validateDirectory()) return;

    try {
      console.log(formData);
      const payload = {
        ...(formData.name.trim() && {userName: formData.name}),
        ...(formData.email.trim() && {email: formData.email}),
        ...(formData.phone.trim() && {phone: formData.phone}),
        ...(formData.designation.trim() && {designation: formData.designation}),
        ...(formData.reportingManager.trim() && {reportingManager: formData.reportingManager}),
        ...(formData.description.trim() && {description: formData.description}),
        ...(formData.address.trim() && {address: formData.address}),
        role: 2,
      }
      await apiClient.post('/directory', payload);
      notifications.show({
        title: 'Success',
        message: 'Directory entry created successfully',
        color: 'green',
      });
      setIsCreateModalOpen(false);
      refresh();
      resetForm();
    } catch (error) {
      console.error('Failed to create directory:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to create directory entry',
        color: 'red',
      });
    }
  };

  const handleEditDirectory = async () => {
    if (!validateDirectory() || !currentDirectory) return;

    try {
      await apiClient.patch(`/api/directory/${currentDirectory._id}`, formData);
      notifications.show({
        title: 'Success',
        message: 'Directory entry updated successfully',
        color: 'green',
      });
      setIsEditModalOpen(false);
      refresh();
      resetForm();
    } catch (error) {
      console.error('Failed to update directory:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to update directory entry',
        color: 'red',
      });
    }
  };

  const handleDeleteDirectory = async (directory: Directory) => {
    try {
      const response = await apiClient.get(`/api/directory/${directory._id}/usage`);
      setDirectoryUsage(response.data);
      setDeletingDirectory(directory);
      setIsDeleteModalOpen(true);
    } catch (error) {
      console.error('Failed to fetch directory usage:', error);
    }
  };

  const confirmDeleteDirectory = async () => {
    if (!deletingDirectory) return;

    try {
      await apiClient.delete(`/api/directory/${deletingDirectory._id}`);
      notifications.show({
        title: 'Success',
        message: 'Directory entry deleted successfully',
        color: 'green',
      });
      setIsDeleteModalOpen(false);
      setDeletingDirectory(null);
      setDirectoryUsage(null);
      refresh();
    } catch (error) {
      console.error('Failed to delete directory:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to delete directory entry',
        color: 'red',
      });
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      designation: '',
      description: '',
      email: '',
      phone: '',
      address: '',
      reportingManager: ''
    });
    setCurrentDirectory(null);
  };
  const handleEditClick = (directory: Directory) => {
    console.log(directory);
    setFormData({
      name: directory.userName,
      designation: directory.designation || '',
      description: directory.description || '',
      email: directory.email || '',
      phone: directory.phone || '',
      address: directory.address || '',
      reportingManager: directory.reportingManager?.name || '',
    });
    setCurrentDirectory(directory);
    setIsEditModalOpen(true);
  };

  return (
    <Container size="xl" py="md">
      <Group justify="space-between" mb="lg">
        <div>
          <Title>Directory</Title>
          <Text c="dimmed">Manage committee members and contacts</Text>
        </div>
        <Button
          leftSection={<IconPlus size={14} />}
          onClick={() => setIsCreateModalOpen(true)}
        >
          Create Member
        </Button>
      </Group>

      <Paper p="md" withBorder mb="lg">
        <SearchAndPagination
          search={search}
          onSearchChange={setSearch}
          pagination={pagination}
          onPageChange={setPage}
          onLimitChange={setLimit}
          sortBy={sortBy}
          sortOrder={sortOrder}
          onSortChange={setSorting}
          sortOptions={sortOptions}
          loading={loading}
        />
      </Paper>      {loading ? (
        <Paper p="xl" withBorder>
          <Stack align="center">
            <Loader />
            <Text>Loading directory...</Text>
          </Stack>
        </Paper>
      ) : directories?.length === 0 ? (
        <Paper p="xl" withBorder>
          <Stack align="center">
            <Text c="dimmed">No directory entries found</Text>
            <Button
              variant="light"
              onClick={() => setIsCreateModalOpen(true)}
            >
              Add your first member
            </Button>
          </Stack>
        </Paper>
      ) : (
        <Grid>
          {directories?.map((directory: Directory) => (
            <Grid.Col key={directory._id} span={{ base: 12, md: 6, lg: 4 }}>
              <Card withBorder shadow="sm">
                <Group align="flex-start" justify="space-between" mb="md" wrap="nowrap">
                  <Stack gap="1">
                    <Text size="sm" c="dimmed">
                      {directory.designation}
                    </Text>
                    <Text fw={500} size="lg">
                      {directory.userName}
                    </Text>
                  </Stack>
                  <Group gap="xs" wrap="nowrap">
                    <ActionIcon
                      variant="light"
                      color="blue"
                      onClick={() => handleEditClick(directory)}
                    >
                      <IconEdit size={16} />
                    </ActionIcon>
                    <ActionIcon
                      variant="light"
                      color="red"
                      onClick={() => handleDeleteDirectory(directory)}
                    >
                      <IconTrash size={16} />
                    </ActionIcon>
                  </Group>
                </Group>

                {directory.description && (
                  <Text size="sm" mb="md">
                    {directory.description}
                  </Text>
                )}

                <Stack gap="xs">
                  {
                    directory.reportingManager && (
                      <Text size="sm" mb="md">
                        {directory.reportingManager.name}
                      </Text>
                    )
                  }
                  {directory.email && (
                    <Group gap="xs">
                      <IconMail size={16} />
                      <Text size="sm">{directory.email}</Text>
                    </Group>
                  )}
                  {directory.phone && (
                    <Group gap="xs">
                      <IconPhone size={16} />
                      <Text size="sm">{directory.phone}</Text>
                    </Group>
                  )}
                  {directory.address && (
                    <Group gap="xs">
                      <IconMapPin size={16} />
                      <Text size="sm">{directory.address}</Text>
                    </Group>
                  )}
                </Stack>
              </Card>
            </Grid.Col>
          ))}
        </Grid>
      )}

      {/* Create Directory Modal */}
      <Modal
        opened={isCreateModalOpen}
        onClose={() => {
          setIsCreateModalOpen(false);
          resetForm();
        }}
        title="Add New Member"
        size="lg"
      >
        <DirectoryForm
          formData={formData}
          setFormData={setFormData}
          onSubmit={handleCreateDirectory}
          onCancel={() => {
            setIsCreateModalOpen(false);
            resetForm();
          }}
          title="Create Member"
          directories={directories}
        />
      </Modal>

      {/* Edit Directory Modal */}
      <Modal
        opened={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          resetForm();
        }}
        title="Edit Contact"
        size="lg"
      >
        <DirectoryForm
          formData={formData}
          setFormData={setFormData}
          onSubmit={handleEditDirectory}
          onCancel={() => {
            setIsEditModalOpen(false);
            resetForm();
          }}
          title="Save Changes"
          directories={directories}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        opened={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setDeletingDirectory(null);
          setDirectoryUsage(null);
        }}
        title="Delete Directory Entry"
        size="md"
      >
        <Stack>
          <Alert
            color={directoryUsage && (directoryUsage.committees.length > 0 || directoryUsage.isAssistant.length > 0) ? "orange" : "red"}
            title={directoryUsage && (directoryUsage.committees.length > 0 || directoryUsage.isAssistant.length > 0) ? "Warning: Person is in use" : "Confirm deletion"}
          >
            You are about to delete <strong>"{deletingDirectory?.userName}"</strong> from the directory.
          </Alert>

          {directoryUsage && (directoryUsage.committees.length > 0 || directoryUsage.isAssistant.length > 0) ? (
            <Stack>
              <Text size="sm" fw={500} c="orange">
                This person is currently being used in the following places:
              </Text>

              {directoryUsage.committees.length > 0 && (
                <div>
                  <Text size="sm" fw={500} mb="xs">Committee Member in:</Text>
                  <List size="sm" spacing="xs">
                    {directoryUsage.committees.map((committee, index) => (
                      <List.Item key={index}>{committee}</List.Item>
                    ))}
                  </List>
                </div>
              )}

              {directoryUsage.isAssistant.length > 0 && (
                <div>
                  <Text size="sm" fw={500} mb="xs">Committee Assistant for:</Text>
                  <List size="sm" spacing="xs">
                    {directoryUsage.isAssistant.map((committee, index) => (
                      <List.Item key={index}>{committee}</List.Item>
                    ))}
                  </List>
                </div>
              )}
              <Alert color="orange">
                <Text size="sm">
                  <strong>If you proceed:</strong>
                </Text>
                <List size="sm" spacing="xs" mt="xs">
                  <List.Item>This person will be removed from all committees</List.Item>
                  <List.Item>Any committee where they are the assistant will have no assistant</List.Item>
                  <List.Item>Their task assignments may be affected</List.Item>
                  <List.Item>This action cannot be undone</List.Item>
                </List>
              </Alert>
            </Stack>
          ) : (
            <Text size="sm" c="dimmed">
              This person is not currently assigned to any committees. They can be safely deleted.
            </Text>
          )}

          <Group justify="flex-end" mt="lg">
            <Button
              variant="light"
              onClick={() => {
                setIsDeleteModalOpen(false);
                setDeletingDirectory(null);
                setDirectoryUsage(null);
              }}
            >
              Cancel
            </Button>
            <Button
              color={directoryUsage && (directoryUsage.committees.length > 0 || directoryUsage.isAssistant.length > 0) ? "orange" : "red"}
              onClick={confirmDeleteDirectory}
            >
              {directoryUsage && (directoryUsage.committees.length > 0 || directoryUsage.isAssistant.length > 0)
                ? "Delete Anyway"
                : "Delete Entry"
              }
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  );
}
