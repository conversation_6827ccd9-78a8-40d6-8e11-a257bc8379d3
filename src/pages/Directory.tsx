import { useState, useCallback } from 'react';
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Paper,
  TextInput,
  Textarea,
  Modal,
  Stack,
  Card,
  ActionIcon,
  Grid,
  Loader,
  Select,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { IconPlus, IconEdit, IconTrash, IconMail, IconPhone, IconMapPin } from '@tabler/icons-react';
import { apiClient } from '../config/axios';
import { Directory } from '../types';
import { usePagination } from '../hooks/usePagination';
import { SearchAndPagination } from '../components/SearchAndPagination';

// Move DirectoryForm outside to prevent recreation on every render
const DirectoryForm = ({
  formData,
  setFormData,
  onSubmit,
  onCancel,
  title,
}: {
  formData: any;
  setFormData: (data: any) => void;
  onSubmit: () => void;
  onCancel: () => void;
  title: string;
  directories: Directory[];
}) => (
  <Stack>
    <TextInput
      label="Designation"
      placeholder="Enter designation"
      value={formData.designation}
      onChange={(e) => setFormData({ ...formData, designation: e.target.value })}
      required
    />

    <TextInput
      label="Name"
      placeholder="Enter full name"
      value={formData.name}
      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
      required
    />

    <Textarea
      label="Description"
      placeholder="Enter description"
      value={formData.description}
      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
      minRows={2}
      resize="vertical"
    />

    <TextInput
      label="Email"
      type="email"
      placeholder="Enter email address"
      value={formData.email}
      onChange={(e) => setFormData({ ...formData, email: e.target.value })}
      required
    />

    <TextInput
      label="Phone"
      type="number"
      placeholder="Enter phone number"
      value={formData.phone}
      onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
      required
    />

    <Textarea
      label="Address"
      placeholder="Enter address"
      value={formData.address}
      onChange={(e) => setFormData({ ...formData, address: e.target.value })}
      minRows={2}
      resize="vertical"
    />


    <Group justify="flex-end" mt="md">
      <Button variant="light" onClick={onCancel}>
        Cancel
      </Button>
      <Button onClick={onSubmit}>{title}</Button>
    </Group>
  </Stack>
);

export default function DirectoryPage() {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [currentDirectory, setCurrentDirectory] = useState<Directory | null>(null);
  const [deletingDirectory, setDeletingDirectory] = useState<Directory | null>(null);
  const [isAssigning, setIsAssigning] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    designation: '',
    description: '',
    email: '',
    phone: '',
    address: '',
    reportingManager: ''
  });
  // Fetch function for pagination
  const fetchDirectories = useCallback(async (params: any): Promise<any> => {
    const apiResponse = await apiClient.get('/directory', { params });
    console.log(apiResponse.data.responseObject);
    // const response = {
    //   data: [
    //     {
    //       _id: "6858e6703e5de81bc942a510",
    //       name: "Officer pankaj",
    //       designation: "officer",
    //       description: "description of officer pankaj",
    //       email: "<EMAIL>",
    //       phone: "8580822170",
    //       address: "sector 91 mohali\nmohali punjab",
    //       createdBy: {
    //         _id: "6858e480f64bb683f3b6c40d",
    //         email: "<EMAIL>",
    //         password: "$2b$08$BFkA7IINndY.6Sm25o5dXeTj69SVwnf/SIv3YJCnzPHiDbrzXh40C",
    //         name: "Administrator",
    //         role: 1,
    //         createdAt: "2025-06-23T05:22:08.012Z",
    //         updatedAt: "2025-06-23T05:22:08.012Z",
    //         __v: 0
    //       },
    //       createdAt: "2025-06-23T05:30:24.133Z",
    //       __v: 0
    //     }
    //   ],
    //   pagination: {
    //     page: 1,
    //     limit: 10,
    //     total: 1,
    //     pages: 1
    //   }
    // };
    return apiResponse.data.responseObject;
  }, []);

  const {
    data: directories,
    pagination,
    loading,
    search,
    sortBy,
    sortOrder,
    setPage,
    setLimit,
    setSearch,
    setSorting,
    refresh,
  } = usePagination<Directory>(fetchDirectories, {    sortBy: 'name',
    sortOrder: 'asc'
  });

  const sortOptions = [
    { value: 'name', label: 'Name' },
    { value: 'designation', label: 'Designation' },
    { value: 'email', label: 'Email' },
    { value: 'createdAt', label: 'Created Date' },
  ];

  const validateDirectory = () => {
    if (!formData.designation) {
      notifications.show({
        title: 'Validation Error',
        message: 'Designation is required',
        color: 'red',
      });
      return false;
    }
    if (!formData.name) {
      notifications.show({
        title: 'Validation Error',
        message: 'Member name is required',
        color: 'red',
      });
      return false;
    }
    if (!formData.email) {
      notifications.show({
        title: 'Validation Error',
        message: 'Member email is required',
        color: 'red',
      });
      return false;
    }
    return true;
  };

  const handleCreateDirectory = async () => {
    if (!validateDirectory()) return;

    try {
      console.log(formData);
      const payload = {
        ...(formData.name.trim() && {userName: formData.name}),
        ...(formData.email.trim() && {email: formData.email}),
        ...(formData.phone.trim() && {phone: formData.phone}),
        ...(formData.designation.trim() && {designation: formData.designation}),
        ...(formData.reportingManager.trim() && {reportingManager: formData.reportingManager}),
        ...(formData.description.trim() && {description: formData.description}),
        ...(formData.address.trim() && {address: formData.address}),
        role: 2,
      }
      await apiClient.post('/directory', payload);
      notifications.show({
        title: 'Success',
        message: 'Directory entry created successfully',
        color: 'green',
      });
      setIsCreateModalOpen(false);
      refresh();
      resetForm();
    } catch (error) {
      console.error('Failed to create directory:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to create directory entry',
        color: 'red',
      });
    }
  };

  const handleEditDirectory = async () => {
    if (!validateDirectory() || !currentDirectory) return;

    try {
      const payload = {
        ...(formData.name.trim() && {userName: formData.name}),
        ...(formData.email.trim() && {email: formData.email}),
        ...(formData.phone.trim() && {phone: formData.phone}),
        ...(formData.designation.trim() && {designation: formData.designation}),
        ...(formData.description.trim() && {description: formData.description}),
        ...(formData.address.trim() && {address: formData.address}),
        role: 2,
      }
      await apiClient.patch(`/directory/${currentDirectory._id}`, payload);
      notifications.show({
        title: 'Success',
        message: 'Directory entry updated successfully',
        color: 'green',
      });
      setIsEditModalOpen(false);
      refresh();
      resetForm();
    } catch (error) {
      console.error('Failed to update directory:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to update directory entry',
        color: 'red',
      });
    }
  };

  const handleDeleteDirectory = async (directory: Directory) => {
    try {
      setDeletingDirectory(directory);
      setIsDeleteModalOpen(true);
    } catch (error) {
      console.error('Failed to fetch directory usage:', error);
    }
  };

  const confirmDeleteDirectory = async () => {
    if (!deletingDirectory) return;

    try {
      await apiClient.delete(`/directory/${deletingDirectory._id}`);
      notifications.show({
        title: 'Success',
        message: 'Directory entry deleted successfully',
        color: 'green',
      });
      setIsDeleteModalOpen(false);
      setDeletingDirectory(null);
      refresh();
    } catch (error) {
      console.error('Failed to delete directory:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to delete directory entry',
        color: 'red',
      });
    }
  };

  const handleReportingManagerChange = async (userId: string, rm: string | null) => {
    setIsAssigning(true);
    try {
      const rmArray = [];
      if (rm) {
        rmArray.push(rm);
      }
      const response = await apiClient.patch(`/directory/${userId}/assignBulk`, {
        rm: rmArray
      });
      refresh();
      console.log(response);
    } catch (error) {
      notifications.show({
        title: 'Validation Error',
        message: 'Unable to assign manager',
        color: 'red',
      })
    } finally{
      setIsAssigning(false);
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      designation: '',
      description: '',
      email: '',
      phone: '',
      address: '',
      reportingManager: ''
    });
    setCurrentDirectory(null);
  };
  const handleEditClick = (directory: Directory) => {
    console.log(directory);
    setFormData({
      name: directory.userName,
      designation: directory.designation || '',
      description: directory.description || '',
      email: directory.email || '',
      phone: directory.phone || '',
      address: directory.address || '',
      reportingManager: directory.reportingManager[0]|| '',
    });
    setCurrentDirectory(directory);
    setIsEditModalOpen(true);
  };

  return (
    <Container size="xl" py="md">
      <Group justify="space-between" mb="lg">
        <div>
          <Title>Directory</Title>
          <Text c="dimmed">Manage committee members and contacts</Text>
        </div>
        <Button
          leftSection={<IconPlus size={14} />}
          onClick={() => setIsCreateModalOpen(true)}
        >
          Create Member
        </Button>
      </Group>

      <Paper p="md" withBorder mb="lg">
        <SearchAndPagination
          search={search}
          onSearchChange={setSearch}
          pagination={pagination}
          onPageChange={setPage}
          onLimitChange={setLimit}
          sortBy={sortBy}
          sortOrder={sortOrder}
          onSortChange={setSorting}
          sortOptions={sortOptions}
          loading={loading}
        />
      </Paper>      {loading ? (
        <Paper p="xl" withBorder>
          <Stack align="center">
            <Loader />
            <Text>Loading directory...</Text>
          </Stack>
        </Paper>
      ) : directories?.length === 0 ? (
        <Paper p="xl" withBorder>
          <Stack align="center">
            <Text c="dimmed">No directory entries found</Text>
            <Button
              variant="light"
              onClick={() => setIsCreateModalOpen(true)}
            >
              Add your first member
            </Button>
          </Stack>
        </Paper>
      ) : (
        <Grid>
          {directories?.map((directory: Directory) => (
            <Grid.Col key={directory._id} span={{ base: 12, md: 6, lg: 4 }}>
              <Card withBorder shadow="sm">
                <Group align="flex-start" justify="space-between" mb="md" wrap="nowrap">
                  <Stack gap="1">
                    <Text size="sm" c="dimmed">
                      {directory.designation}
                    </Text>
                    <Text fw={500} size="lg">
                      {directory.userName}
                    </Text>
                  </Stack>
                  <Group gap="xs" wrap="nowrap">
                    <ActionIcon
                      variant="light"
                      color="blue"
                      onClick={() => handleEditClick(directory)}
                    >
                      <IconEdit size={16} />
                    </ActionIcon>
                    <ActionIcon
                      variant="light"
                      color="red"
                      onClick={() => handleDeleteDirectory(directory)}
                    >
                      <IconTrash size={16} />
                    </ActionIcon>
                  </Group>
                </Group>

                {directory.description && (
                  <Text size="sm" mb="md">
                    {directory.description}
                  </Text>
                )}

                <Stack gap="xs">
                  {directory.email && (
                    <Group gap="xs">
                      <IconMail size={16} />
                      <Text size="sm">{directory.email}</Text>
                    </Group>
                  )}
                  {directory.phone && (
                    <Group gap="xs">
                      <IconPhone size={16} />
                      <Text size="sm">{directory.phone}</Text>
                    </Group>
                  )}
                  {directory.address && (
                    <Group gap="xs">
                      <IconMapPin size={16} />
                      <Text size="sm">{directory.address}</Text>
                    </Group>
                  )}
                  <Group>
                    <Text size="sm" mb={4}>
                      Reporting Manager
                    </Text>
                    <Select
                      allowDeselect
                      placeholder="no manager selected"
                      data={directories
                        .filter((d) => d._id !== directory._id)
                        .map((d) => ({ value: d._id, label: d.userName }))}
                      value={directory.reportingManager ? directory.reportingManager[0] : null}
                      onChange={(value) => {
                        handleReportingManagerChange(directory._id, value)
                      }}
                      size="xs"
                      searchable
                      disabled={isAssigning}
                    />
                  </Group>
                </Stack>
              </Card>
            </Grid.Col>
          ))}
        </Grid>
      )}

      {/* Create Directory Modal */}
      <Modal
        opened={isCreateModalOpen}
        onClose={() => {
          setIsCreateModalOpen(false);
          resetForm();
        }}
        title="Add New Member"
        size="lg"
      >
        <DirectoryForm
          formData={formData}
          setFormData={setFormData}
          onSubmit={handleCreateDirectory}
          onCancel={() => {
            setIsCreateModalOpen(false);
            resetForm();
          }}
          title="Create Member"
          directories={directories}
        />
      </Modal>

      {/* Edit Directory Modal */}
      <Modal
        opened={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          resetForm();
        }}
        title="Edit Contact"
        size="lg"
      >
        <DirectoryForm
          formData={formData}
          setFormData={setFormData}
          onSubmit={handleEditDirectory}
          onCancel={() => {
            setIsEditModalOpen(false);
            resetForm();
          }}
          title="Save Changes"
          directories={directories}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        opened={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setDeletingDirectory(null);
        }}
        title="Delete Directory Entry"
        size="md"
      >
        <Stack>

          <Group justify="flex-end" mt="lg">
            <Button
              variant="light"
              onClick={() => {
                setIsDeleteModalOpen(false);
                setDeletingDirectory(null);
              }}
            >
              Cancel
            </Button>
            <Button
              color={"red"}
              onClick={confirmDeleteDirectory}
            >
                Delete Entry
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  );
}
