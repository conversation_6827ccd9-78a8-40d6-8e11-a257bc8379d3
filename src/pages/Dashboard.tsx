import { useEffect, useState } from "react";
import { apiClient } from "../config/axios";
import {
  Container,
  Text,
  SimpleGrid,  Group,
  Stack,
  Title,
  Card,
  Grid,
  Skeleton,
  Table,
  Paper,
  Tabs,
}from "@mantine/core";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart } from '@mantine/charts';
import { notifications } from '@mantine/notifications';
import {
  IconList,
  IconContract,
  IconUsers,
  IconFileInfo,
  IconDeviceDesktopAnalytics,
} from '@tabler/icons-react';
import { Ward, Complaint } from "../types";
import { useNavigate } from "react-router-dom";

interface DashboardStats {
  totalWards: number;
}
interface ChartTooltipProps {
  label: string;
  payload: Record<string, any>[] | undefined;
}

function ChartTooltip({ label, payload }: ChartTooltipProps) {
  if (!payload) return null;

  return (
    <Paper px="md" py="sm" withBorder shadow="md" radius="md" style={{ backgroundColor: 'white'}}>
      <Text fw={500} mb={5}>
        {label}
      </Text>
      {payload.map((item: any) => (
        <Text key={item.name} c={item.color} fz="sm">
          {item.name}: {item.value}
        </Text>
      ))}
    </Paper>
  );
}


export default function Dashboard() {
  const [loading, setLoading] = useState(true);
  const [complaints, setComplaints] = useState<Complaint[]>([]);
  const navigate = useNavigate();
  const [complaintGraphData, setComplaintGraphData] = useState([]);

  const [stats, setStats] = useState<DashboardStats>({
    totalWards: 0,
  });


  useEffect(() => {
    fetchData();
    fetchGraphsData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {      // Fetch wards first
      const wardsRes = await apiClient.get("/ward");
      const wards: Ward[] = wardsRes.data.responseObject.data;
      const totalWards = wards.length;
      setStats({
        totalWards,
      });

      const complaintsRes = await apiClient.get("/complaints", {params:{
        limit: 10,
        sortBy: "createdAt"
      }});
      setComplaints(complaintsRes.data.responseObject.data);
    } catch (error) {
      console.error("Failed to fetch data", error);
      notifications.show({
        title: "Error",
        message: "Failed to load dashboard data",
        color: "red",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchGraphsData = async () => {
    const response = await apiClient.get("/complaints/frequency-graph")
    setComplaintGraphData(response.data.responseObject);
  }

  if (loading) {
    return (
      <Container size="xl" py="xl">
        {/* Dashboard Title Skeleton */}
        <Group justify="space-between" mb="xl">
          <div>
            <Skeleton height={36} width={500} mb={8} />
            <Skeleton height={16} width={300} />
          </div>
        </Group>

        {/* Key Metrics Skeleton */}
        <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }} spacing="md" mb="xl">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={`metric-skeleton-${i}`} withBorder p="lg">
              <Group justify="space-between">
                <div>
                  <Skeleton height={14} width={80} mb={10} />
                  <Skeleton height={28} width={40} />
                </div>
                <Skeleton height={32} width={32} circle />
              </Group>
              <Skeleton height={8} mt="md" />
              <Skeleton height={14} width="40%" mt="xs" />
            </Card>
          ))}
        </SimpleGrid>
        
        {/* Upcoming Meetings & Tasks Skeleton */}
        <Grid gutter="md" mb="xl">
          {Array.from({ length: 2 }).map((_, i) => (
            <Grid.Col key={`section-skeleton-${i}`} span={{ base: 12, md: 6 }}>
              <Card withBorder p="lg" h={400}>
                <Group justify="space-between" mb="md">
                  <Skeleton height={24} width={180} />
                  <Skeleton height={24} width={24} />
                </Group>
                <Stack gap="md">
                  {Array.from({ length: 3 }).map((_, j) => (
                    <Card key={`item-skeleton-${i}-${j}`} p="md" withBorder radius="sm">
                      <Group justify="space-between" wrap="nowrap">
                        <div style={{ width: '60%' }}>
                          <Skeleton height={16} width="80%" mb={8} />
                          <Skeleton height={14} width="50%" />
                        </div>
                        <div style={{ textAlign: 'right', width: '30%' }}>
                          <Skeleton height={16} width="100%" mb={8} />
                          <Skeleton height={12} width="70%" />
                        </div>
                      </Group>
                    </Card>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>
          ))}
        </Grid>

        {/* Ward Performance Skeleton */}
        <SimpleGrid cols={{ base: 1, lg: 2 }} spacing="md" mb="xl">
          {Array.from({ length: 2 }).map((_, i) => (
            <Card key={`performance-skeleton-${i}`} withBorder p="lg">
              <Group justify="space-between" mb="md">
                <div>
                  <Skeleton height={14} width={120} mb={8} />
                  <Skeleton height={20} width={180} />
                </div>
                <Skeleton height={24} width={24} circle />
              </Group>
              <Group justify="space-between" mb="xs">
                <Skeleton height={14} width={100} />
                <Skeleton height={14} width={60} />
              </Group>
              <Skeleton height={8} mb="md" />
              <Group justify="space-between">
                <Skeleton height={12} width={80} />
                <Skeleton height={24} width={60} />
              </Group>
            </Card>
          ))}
        </SimpleGrid>
      </Container>
    );
  }

  return (
    <Container size="xl" py="xl">
      <Group justify="space-between" mb="xl">
        <div>
          <Title order={1}>Ward complaints & Monitoring Platform</Title>
          <Text c="dimmed">Overview of your wards and complaints</Text>
        </div>
      </Group>


      {/* Key Metrics */}
      <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }} spacing="md" mb="xl">
        <Card withBorder p="lg" onClick={() => navigate("/wards")} style={{cursor:"pointer"}}>
          <Group justify="space-between">
            <div>
              <Text size="xs" c="dimmed" tt="uppercase" fw={700}>Total Wards</Text>
              <Text size="xl" fw={700}>{stats.totalWards}</Text>
            </div>
            <IconList size={32} stroke={1.5} />
          </Group>
        </Card>

        <Card withBorder p="lg" onClick={() => navigate("/directory")} style={{cursor:"pointer"}}>
          <Group justify="space-between">
            <div>
              <Text size="xs" c="dimmed" tt="uppercase" fw={700}>Total Resolvers</Text>
              <Text size="xl" fw={700}>2</Text>
            </div>
            <IconUsers size={32} stroke={1.5} />
          </Group>
        </Card>
        
        <Card withBorder p="lg" onClick={() => navigate("/complaints")} style={{cursor:"pointer"}}>
          <Group justify="space-between">
            <div>
              <Text size="xs" c="dimmed" tt="uppercase" fw={700}>Total Complaints</Text>
              <Text size="xl" fw={700}>2</Text>
            </div>
            <IconContract size={32} stroke={1.5} />
          </Group>
        </Card>

        <Card withBorder p="lg">
          <Group justify="space-between">
            <div>
              <Text size="xs" c="dimmed" tt="uppercase" fw={700}>Unresolved Complaints</Text>
              <Text size="xl" fw={700}>1</Text>
            </div>
            <IconFileInfo size={32} stroke={1.5} />
          </Group>
        </Card>
      </SimpleGrid>

       <Card withBorder p="lg" mb="xl">
        <Title order={4} mb="md">
          <Group>
            Statistic Overview
            <IconDeviceDesktopAnalytics size={24} stroke={1.5} /> 
          </Group>
        </Title>
        <Tabs defaultValue={"complaintsGraph"}>
          <Tabs.List>
            <Tabs.Tab value="complaintsGraph">Complaints Overview</Tabs.Tab>
            <Tabs.Tab value="wardGraph">Wards Overview</Tabs.Tab>
          </Tabs.List>
          <Tabs.Panel value="complaintsGraph" pt="md"> 
            <LineChart
              h={300}
              data={complaintGraphData}
              dataKey="_id"
              series={[
                { name: 'count', color: 'indigo.6' },
              ]}
              strokeWidth={2}
              curveType="natural"
              xAxisLabel="Date"
              yAxisLabel="No. of complaints"
              valueFormatter={(value) => `${value}`}
              tooltipProps={{
                content: ({ label, payload }) => <ChartTooltip label={label} payload={payload} />,
              }}
            />
          </Tabs.Panel>
          <Tabs.Panel value="wardGraph" pt="md">
            <BarChart 
              h={300}
              data={WardGraphData}
              dataKey="ward"  
              series={[{name:'complaints', color: 'blue'}]}
              xAxisLabel="Wards"
              yAxisLabel="No. of complaints"
              tooltipProps={{
                content: ({ label, payload }) => <ChartTooltip label={label} payload={payload} />,
                wrapperStyle: { backgroundColor: 'white' }
              }}
              
            />
          </Tabs.Panel>
        </Tabs>
      </Card>


      <Card withBorder p="lg" mb="xl">
        <Title order={4} mb="md">Recent Complaints</Title>
        <Table striped highlightOnHover>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>Title</Table.Th>
              <Table.Th>Status</Table.Th>
              <Table.Th>Ward</Table.Th>
              <Table.Th>Date</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {complaints.length === 0 ? (
              <Table.Tr>
                <Table.Td colSpan={4}>
                  <Text c="dimmed">No recent complaints</Text>
                </Table.Td>
              </Table.Tr>
            ) : (
              complaints.map((complaint) => (
                <Table.Tr key={complaint._id}>
                  <Table.Td>{complaint.title}</Table.Td>
                  <Table.Td>{complaint.complaintStatus}</Table.Td>
                  <Table.Td>{complaint.location.locationMetaData.ward.title || "-"}</Table.Td>
                  <Table.Td>{new Date(complaint.createdAt).toLocaleDateString()}</Table.Td>
                </Table.Tr>
              ))
            )}
          </Table.Tbody>
        </Table>
      </Card>

    </Container>
  );
}


// interface ComplaintGraphDataType {
//     _id: string;
//     count: number;
// }

const WardGraphData = [
  {
    ward: 10,
    complaints: 15,
  },
  {
    ward: 20,
    complaints: 10,
  },
  {
    ward: 30,
    complaints: 5,
  },
  {
    ward: 40,
    complaints: 5,
  },
  {
    ward: 12,
    complaints: 40,
  },
  {
    ward: 7,
    complaints: 30,
  },
]