import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { apiClient } from "../config/axios";
import {
  Button,
  Container,
  Text,
  Paper,
  SimpleGrid,  Group,
  Stack,
  Badge,
  Progress,
  Title,
  Card,
  Grid,
  Skeleton,
}from "@mantine/core";
import { notifications } from '@mantine/notifications';
import {
  IconCalendar,
  IconList,
  IconTrendingUp,
  IconTrendingDown,
  IconClock,
  IconChevronRight,
  IconAlertTriangle,
  IconBrightnessAuto,
  IconContract,
  IconUsers,
} from '@tabler/icons-react';
import { Task, Meeting, Ward, TaskResponse } from "../types";

const priorityColors: Record<string, string> = {
  low: 'blue',
  medium: 'yellow',
  high: 'red'
};

interface WardPerformance {
  ward: Ward;
  totalTasks: number;
  completedTasks: number;
  completionRate: number;
  upcomingMeetings: number;
}

interface DashboardStats {
  totalWards: number;
}

type UpcomingWardMeeting = Ward & { meetingTime: Date }

export default function Dashboard() {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);

  const [stats, setStats] = useState<DashboardStats>({
    totalWards: 0,
  });


  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {      // Fetch wards first
      const wardsRes = await apiClient.get<{ data: Ward[]; total: number; page: number; limit: number; }>("/api/ward");
      const wards: Ward[] = wardsRes.data.data;
      const totalWards = wards.length;
      console.log(wards);
      setStats({
        totalWards,
      });
    } catch (error) {
      console.error("Failed to fetch data", error);
      notifications.show({
        title: "Error",
        message: "Failed to load dashboard data",
        color: "red",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleViewWard = (wardId: string) => {
    navigate(`/ward/${wardId}/meetings`);
  };


  if (loading) {
    return (
      <Container size="xl" py="xl">
        {/* Dashboard Title Skeleton */}
        <Group justify="space-between" mb="xl">
          <div>
            <Skeleton height={36} width={500} mb={8} />
            <Skeleton height={16} width={300} />
          </div>
        </Group>

        {/* Key Metrics Skeleton */}
        <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }} spacing="md" mb="xl">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={`metric-skeleton-${i}`} withBorder p="lg">
              <Group justify="space-between">
                <div>
                  <Skeleton height={14} width={80} mb={10} />
                  <Skeleton height={28} width={40} />
                </div>
                <Skeleton height={32} width={32} circle />
              </Group>
              <Skeleton height={8} mt="md" />
              <Skeleton height={14} width="40%" mt="xs" />
            </Card>
          ))}
        </SimpleGrid>
        
        {/* Upcoming Meetings & Tasks Skeleton */}
        <Grid gutter="md" mb="xl">
          {Array.from({ length: 2 }).map((_, i) => (
            <Grid.Col key={`section-skeleton-${i}`} span={{ base: 12, md: 6 }}>
              <Card withBorder p="lg" h={400}>
                <Group justify="space-between" mb="md">
                  <Skeleton height={24} width={180} />
                  <Skeleton height={24} width={24} />
                </Group>
                <Stack gap="md">
                  {Array.from({ length: 3 }).map((_, j) => (
                    <Card key={`item-skeleton-${i}-${j}`} p="md" withBorder radius="sm">
                      <Group justify="space-between" wrap="nowrap">
                        <div style={{ width: '60%' }}>
                          <Skeleton height={16} width="80%" mb={8} />
                          <Skeleton height={14} width="50%" />
                        </div>
                        <div style={{ textAlign: 'right', width: '30%' }}>
                          <Skeleton height={16} width="100%" mb={8} />
                          <Skeleton height={12} width="70%" />
                        </div>
                      </Group>
                    </Card>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>
          ))}
        </Grid>

        {/* Ward Performance Skeleton */}
        <SimpleGrid cols={{ base: 1, lg: 2 }} spacing="md" mb="xl">
          {Array.from({ length: 2 }).map((_, i) => (
            <Card key={`performance-skeleton-${i}`} withBorder p="lg">
              <Group justify="space-between" mb="md">
                <div>
                  <Skeleton height={14} width={120} mb={8} />
                  <Skeleton height={20} width={180} />
                </div>
                <Skeleton height={24} width={24} circle />
              </Group>
              <Group justify="space-between" mb="xs">
                <Skeleton height={14} width={100} />
                <Skeleton height={14} width={60} />
              </Group>
              <Skeleton height={8} mb="md" />
              <Group justify="space-between">
                <Skeleton height={12} width={80} />
                <Skeleton height={24} width={60} />
              </Group>
            </Card>
          ))}
        </SimpleGrid>
      </Container>
    );
  }

  return (
    <Container size="xl" py="xl">
      <Group justify="space-between" mb="xl">
        <div>
          <Title order={1}>Ward complaints & Monitoring Platform</Title>
          <Text c="dimmed">Overview of your wards and complaints</Text>
        </div>
      </Group>


      {/* Key Metrics */}
      <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }} spacing="md" mb="xl">
        <Card withBorder p="lg">
          <Group justify="space-between">
            <div>
              <Text size="xs" c="dimmed" tt="uppercase" fw={700}>Total Wards</Text>
              <Text size="xl" fw={700}>{stats.totalWards}</Text>
            </div>
            <IconList size={32} stroke={1.5} />
          </Group>
        </Card>

        <Card withBorder p="lg">
          <Group justify="space-between">
            <div>
              <Text size="xs" c="dimmed" tt="uppercase" fw={700}>Total Resolvers</Text>
              <Text size="xl" fw={700}>{stats.totalWards}</Text>
            </div>
            <IconUsers size={32} stroke={1.5} />
          </Group>
        </Card>
        
        <Card withBorder p="lg">
          <Group justify="space-between">
            <div>
              <Text size="xs" c="dimmed" tt="uppercase" fw={700}>Total Complaints</Text>
              <Text size="xl" fw={700}>{stats.totalWards}</Text>
            </div>
            <IconContract size={32} stroke={1.5} />
          </Group>
        </Card>
      </SimpleGrid>

    </Container>
  );
}
