import { Navigate } from "react-router-dom";
import Login from "../pages/Login";
import Register from "../pages/Register";
import Wards from "../pages/Wards";
import Meetings from "../pages/Meetings";
import Directory from "../pages/Directory";
import MeetingDetails from "../pages/MeetingDetails";

export const publicRoutes = [
  { path: "/login", element: <Login /> },
  { path: "/register", element: <Register /> },
];

export const privateRoutes = [
  { path: "/", element: <Navigate to="/wards" replace /> },
  { path: "/wards", element: <Wards /> },
  { path: "/ward/:wardId/meetings", element: <Meetings /> },
  { path: "/ward/:wardId/meetings/:meetingId", element: <MeetingDetails /> },
  { path: "/directory", element: <Directory /> },
];
