import { MantineProvider, MantineThemeOverride } from "@mantine/core";
import { Notifications } from "@mantine/notifications";
import { ModalsProvider } from "@mantine/modals";
import { BrowserRouter } from "react-router-dom";
import { AuthProvider } from "./contexts/AuthContext";
import AppLayout from "./components/AppLayout";

const theme: MantineThemeOverride = {
  primaryColor: "blue",
  defaultRadius: "sm",
  fontFamily: "Inter, sans-serif",
  white: "#fff",
  black: "#1A1B1E",
  components: {
    Button: {
      defaultProps: {
        size: "sm",
      },
      styles: {
        root: {
          fontWeight: 500,
          height: 32,
        },
      },
    },
    TextInput: {
      defaultProps: {
        size: "sm",
      },
    },
    PasswordInput: {
      defaultProps: {
        size: "sm",
      },
    },
    Select: {
      defaultProps: {
        size: "sm",
      },
    },
    Textarea: {
      defaultProps: {
        size: "sm",
      },
    },
    Paper: {
      defaultProps: {
        p: "md",
        radius: "sm",
      },
    },
    Card: {
      defaultProps: {
        padding: "md",
        radius: "sm",
      },
    },
    Badge: {
      defaultProps: {
        size: "sm",
      },
    },
    ActionIcon: {
      defaultProps: {
        size: "sm",
      },
    },
    Container: {
      defaultProps: {
        size: "xl",
      },
    },
  },
};

function App() {
  return (
    <MantineProvider theme={theme}>
      <ModalsProvider>
        <Notifications position="top-right" zIndex={2000} />
        <BrowserRouter>
          <AuthProvider>
            <AppLayout />
          </AuthProvider>
        </BrowserRouter>
      </ModalsProvider>
    </MantineProvider>
  );
}

export default App;
