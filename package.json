{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"@emotion/react": "^11.14.0", "@mantine/core": "^8.1.0", "@mantine/dates": "^8.1.0", "@mantine/form": "^8.1.0", "@mantine/hooks": "^8.1.0", "@mantine/modals": "^8.1.0", "@mantine/notifications": "^8.1.0", "@tabler/icons-react": "^3.34.0", "axios": "^1.9.0", "dayjs": "^1.11.13", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^24.0.0", "@types/react": "^19.1.7", "@types/react-dom": "^19.1.6", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "^5.8.3", "vite": "^6.3.5"}}